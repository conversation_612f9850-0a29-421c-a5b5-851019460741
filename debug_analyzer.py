"""Debug and Error Analysis Module for RAG 2.0 Code Analyzer."""

import re
import ast
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CodeIssue:
    """Represents a code issue or potential bug."""
    file_path: str
    line_number: int
    issue_type: str
    severity: str  # 'high', 'medium', 'low'
    description: str
    suggestion: str
    code_snippet: str


@dataclass
class DebugAnalysis:
    """Results of debug analysis."""
    issues: List[CodeIssue]
    summary: str
    recommendations: List[str]
    code_quality_score: float


class DebugAnalyzer:
    """Comprehensive debug and error analysis for code."""
    
    def __init__(self):
        """Initialize the debug analyzer."""
        self.common_patterns = {
            'python': {
                'sql_injection': [
                    r'execute\s*\(\s*["\'].*%.*["\']',
                    r'cursor\.execute\s*\(\s*["\'].*\+.*["\']'
                ],
                'hardcoded_secrets': [
                    r'password\s*=\s*["\'][^"\']+["\']',
                    r'api_key\s*=\s*["\'][^"\']+["\']',
                    r'secret\s*=\s*["\'][^"\']+["\']'
                ],
                'exception_handling': [
                    r'except\s*:',
                    r'except\s+Exception\s*:'
                ],
                'resource_leaks': [
                    r'open\s*\([^)]*\)(?!\s*with)',
                    r'\.connect\s*\([^)]*\)(?!\s*with)'
                ],
                'deprecated_functions': [
                    r'imp\.load_source',
                    r'platform\.dist',
                    r'asyncio\.coroutine'
                ]
            },
            'javascript': {
                'security_issues': [
                    r'eval\s*\(',
                    r'innerHTML\s*=',
                    r'document\.write\s*\('
                ],
                'async_issues': [
                    r'async\s+function.*(?!await)',
                    r'\.then\s*\(\s*\)\s*\.catch'
                ],
                'console_logs': [
                    r'console\.log\s*\(',
                    r'console\.debug\s*\('
                ]
            },
            'java': {
                'null_pointer': [
                    r'\.equals\s*\(\s*[^)]*\)(?!\s*!=\s*null)',
                    r'[^=!]\s*==\s*null'
                ],
                'resource_leaks': [
                    r'new\s+FileInputStream\s*\([^)]*\)(?!\s*try)',
                    r'new\s+Connection\s*\([^)]*\)(?!\s*try)'
                ],
                'deprecated': [
                    r'new\s+Date\s*\(\s*\)',
                    r'\.finalize\s*\(\s*\)'
                ]
            }
        }
    
    def analyze_code_for_issues(self, documents: List[Dict[str, Any]]) -> DebugAnalysis:
        """Analyze code documents for potential issues."""
        all_issues = []
        
        for doc in documents:
            file_path = doc.get('file', 'unknown')
            content = doc.get('content', '')
            language = doc.get('language', 'text')
            
            # Analyze for common issues
            issues = self._analyze_file_for_issues(file_path, content, language)
            all_issues.extend(issues)
        
        # Generate summary and recommendations
        summary = self._generate_summary(all_issues)
        recommendations = self._generate_recommendations(all_issues)
        quality_score = self._calculate_quality_score(all_issues, len(documents))
        
        return DebugAnalysis(
            issues=all_issues,
            summary=summary,
            recommendations=recommendations,
            code_quality_score=quality_score
        )
    
    def _analyze_file_for_issues(self, file_path: str, content: str, language: str) -> List[CodeIssue]:
        """Analyze a single file for issues."""
        issues = []
        lines = content.split('\n')
        
        # Get patterns for the language
        patterns = self.common_patterns.get(language, {})
        
        for line_num, line in enumerate(lines, 1):
            # Check each pattern category
            for issue_type, pattern_list in patterns.items():
                for pattern in pattern_list:
                    if re.search(pattern, line, re.IGNORECASE):
                        issue = self._create_issue(
                            file_path, line_num, issue_type, line, pattern, language
                        )
                        if issue:
                            issues.append(issue)
        
        # Language-specific analysis
        if language == 'python':
            issues.extend(self._analyze_python_specific(file_path, content))
        elif language == 'javascript':
            issues.extend(self._analyze_javascript_specific(file_path, content))
        
        return issues
    
    def _create_issue(self, file_path: str, line_num: int, issue_type: str, 
                     line: str, pattern: str, language: str) -> Optional[CodeIssue]:
        """Create a CodeIssue object with appropriate details."""
        severity_map = {
            'sql_injection': 'high',
            'hardcoded_secrets': 'high',
            'security_issues': 'high',
            'null_pointer': 'medium',
            'exception_handling': 'medium',
            'resource_leaks': 'medium',
            'async_issues': 'medium',
            'deprecated_functions': 'low',
            'deprecated': 'low',
            'console_logs': 'low'
        }
        
        description_map = {
            'sql_injection': 'Potential SQL injection vulnerability detected',
            'hardcoded_secrets': 'Hardcoded credentials or secrets found',
            'security_issues': 'Security vulnerability detected',
            'null_pointer': 'Potential null pointer exception',
            'exception_handling': 'Overly broad exception handling',
            'resource_leaks': 'Potential resource leak - missing proper cleanup',
            'async_issues': 'Async/await pattern issue',
            'deprecated_functions': 'Usage of deprecated function',
            'deprecated': 'Usage of deprecated API',
            'console_logs': 'Debug console statement left in code'
        }
        
        suggestion_map = {
            'sql_injection': 'Use parameterized queries or prepared statements',
            'hardcoded_secrets': 'Move secrets to environment variables or secure config',
            'security_issues': 'Use secure alternatives and validate input',
            'null_pointer': 'Add null checks or use Optional types',
            'exception_handling': 'Catch specific exceptions instead of generic ones',
            'resource_leaks': 'Use try-with-resources or context managers',
            'async_issues': 'Ensure proper async/await usage',
            'deprecated_functions': 'Update to use current API alternatives',
            'deprecated': 'Update to use current API alternatives',
            'console_logs': 'Remove debug statements or use proper logging'
        }
        
        return CodeIssue(
            file_path=file_path,
            line_number=line_num,
            issue_type=issue_type,
            severity=severity_map.get(issue_type, 'medium'),
            description=description_map.get(issue_type, f'{issue_type} issue detected'),
            suggestion=suggestion_map.get(issue_type, 'Review and fix this issue'),
            code_snippet=line.strip()
        )
    
    def _analyze_python_specific(self, file_path: str, content: str) -> List[CodeIssue]:
        """Python-specific analysis using AST."""
        issues = []
        
        try:
            tree = ast.parse(content)
            
            # Check for common Python issues
            for node in ast.walk(tree):
                # Check for bare except clauses
                if isinstance(node, ast.ExceptHandler) and node.type is None:
                    issues.append(CodeIssue(
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='bare_except',
                        severity='medium',
                        description='Bare except clause catches all exceptions',
                        suggestion='Catch specific exception types',
                        code_snippet=f'Line {node.lineno}: except:'
                    ))
                
                # Check for unused variables (simple heuristic)
                if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                    if node.id.startswith('_') and not node.id.startswith('__'):
                        # This is a simple check - could be improved
                        pass
        
        except SyntaxError as e:
            issues.append(CodeIssue(
                file_path=file_path,
                line_number=e.lineno or 1,
                issue_type='syntax_error',
                severity='high',
                description=f'Syntax error: {e.msg}',
                suggestion='Fix the syntax error',
                code_snippet=f'Line {e.lineno}: {e.text}' if e.text else 'Syntax error'
            ))
        
        return issues
    
    def _analyze_javascript_specific(self, file_path: str, content: str) -> List[CodeIssue]:
        """JavaScript-specific analysis."""
        issues = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # Check for var usage (should use let/const)
            if re.search(r'\bvar\s+\w+', line):
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=line_num,
                    issue_type='var_usage',
                    severity='low',
                    description='Usage of var instead of let/const',
                    suggestion='Use let or const instead of var',
                    code_snippet=line.strip()
                ))
            
            # Check for == instead of ===
            if re.search(r'[^=!]==[^=]', line):
                issues.append(CodeIssue(
                    file_path=file_path,
                    line_number=line_num,
                    issue_type='loose_equality',
                    severity='medium',
                    description='Usage of loose equality (==) instead of strict equality (===)',
                    suggestion='Use === for strict equality comparison',
                    code_snippet=line.strip()
                ))
        
        return issues
    
    def _generate_summary(self, issues: List[CodeIssue]) -> str:
        """Generate a summary of all issues found."""
        if not issues:
            return "✅ No significant issues found in the analyzed code."
        
        high_count = sum(1 for issue in issues if issue.severity == 'high')
        medium_count = sum(1 for issue in issues if issue.severity == 'medium')
        low_count = sum(1 for issue in issues if issue.severity == 'low')
        
        summary = f"🔍 **Debug Analysis Summary:**\n\n"
        summary += f"Found **{len(issues)} total issues**:\n"
        summary += f"• 🔴 **{high_count} high severity** issues\n"
        summary += f"• 🟡 **{medium_count} medium severity** issues\n"
        summary += f"• 🟢 **{low_count} low severity** issues\n\n"
        
        # Group by issue type
        issue_types = {}
        for issue in issues:
            issue_types[issue.issue_type] = issue_types.get(issue.issue_type, 0) + 1
        
        if issue_types:
            summary += "**Most common issues:**\n"
            for issue_type, count in sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                summary += f"• {issue_type.replace('_', ' ').title()}: {count} occurrences\n"
        
        return summary
    
    def _generate_recommendations(self, issues: List[CodeIssue]) -> List[str]:
        """Generate recommendations based on found issues."""
        recommendations = []
        
        # High priority recommendations
        high_issues = [issue for issue in issues if issue.severity == 'high']
        if high_issues:
            recommendations.append("🚨 **Immediate Action Required:** Address all high-severity security issues")
        
        # Common issue patterns
        issue_counts = {}
        for issue in issues:
            issue_counts[issue.issue_type] = issue_counts.get(issue.issue_type, 0) + 1
        
        if issue_counts.get('hardcoded_secrets', 0) > 0:
            recommendations.append("🔐 Implement proper secrets management using environment variables")
        
        if issue_counts.get('exception_handling', 0) > 2:
            recommendations.append("⚠️ Review exception handling patterns - avoid catching generic exceptions")
        
        if issue_counts.get('resource_leaks', 0) > 0:
            recommendations.append("🔧 Implement proper resource management with context managers")
        
        if issue_counts.get('console_logs', 0) > 3:
            recommendations.append("📝 Replace console.log statements with proper logging framework")
        
        # General recommendations
        if len(issues) > 10:
            recommendations.append("🔍 Consider implementing automated code quality checks in CI/CD")
        
        if not recommendations:
            recommendations.append("✅ Code quality looks good! Consider regular code reviews to maintain standards")
        
        return recommendations
    
    def _calculate_quality_score(self, issues: List[CodeIssue], file_count: int) -> float:
        """Calculate a code quality score (0-100)."""
        if file_count == 0:
            return 0.0
        
        # Base score
        score = 100.0
        
        # Deduct points for issues
        for issue in issues:
            if issue.severity == 'high':
                score -= 10
            elif issue.severity == 'medium':
                score -= 5
            else:  # low
                score -= 2
        
        # Normalize by file count
        if file_count > 1:
            score = score * (1 + (file_count - 1) * 0.1)  # Slight bonus for larger codebases
        
        return max(0.0, min(100.0, score))
