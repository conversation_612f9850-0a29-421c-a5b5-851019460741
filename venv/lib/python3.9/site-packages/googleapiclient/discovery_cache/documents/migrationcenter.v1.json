{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://migrationcenter.googleapis.com/", "batchPath": "batch", "canonicalName": "Migration Center API", "description": "A unified platform that helps you accelerate your end-to-end cloud journey from your current on-premises or cloud environments to Google Cloud.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/migration-center", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "migrationcenter:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://migrationcenter.mtls.googleapis.com/", "name": "migrationcenter", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSettings": {"description": "Gets the details of regional settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "GET", "id": "migrationcenter.projects.locations.getSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "migrationcenter.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSettings": {"description": "Updates the regional-level project settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.updateSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Settings` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Settings"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"assets": {"methods": {"aggregateValues": {"description": "Aggregates the requested fields based on provided function.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets:aggregateValues", "httpMethod": "POST", "id": "migrationcenter.projects.locations.assets.aggregateValues", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent value for `AggregateAssetsValuesRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assets:aggregateValues", "request": {"$ref": "AggregateAssetsValuesRequest"}, "response": {"$ref": "AggregateAssetsValuesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "batchDelete": {"description": "Deletes list of Assets.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets:batchDelete", "httpMethod": "POST", "id": "migrationcenter.projects.locations.assets.batchDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent value for batch asset delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assets:batchDelete", "request": {"$ref": "BatchDeleteAssetsRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "batchUpdate": {"description": "Updates the parameters of a list of assets.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets:batchUpdate", "httpMethod": "POST", "id": "migrationcenter.projects.locations.assets.batchUpdate", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent value for batch asset update.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assets:batchUpdate", "request": {"$ref": "BatchUpdateAssetsRequest"}, "response": {"$ref": "BatchUpdateAssetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an asset.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets/{assetsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.assets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/assets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of an asset.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets/{assetsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.assets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/assets/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View of the assets. Defaults to BASIC.", "enum": ["ASSET_VIEW_UNSPECIFIED", "ASSET_VIEW_BASIC", "ASSET_VIEW_FULL", "ASSET_VIEW_STANDARD", "ASSET_VIEW_UI", "ASSET_VIEW_LABELS"], "enumDescriptions": ["The asset view is not specified. The API displays the basic view by default.", "The asset view includes only basic metadata of the asset.", "The asset view includes all the metadata of an asset and performance data.", "The asset view includes the standard metadata of an asset.", "The asset view includes fields needed by UI.", "The asset view includes asset name and labels."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the assets in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets", "httpMethod": "GET", "id": "migrationcenter.projects.locations.assets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListAssetsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showHidden": {"description": "Optional. When this value is set to 'true,' the response will include all assets, including those that are hidden.", "location": "query", "type": "boolean"}, "view": {"description": "View of the assets. Defaults to BASIC.", "enum": ["ASSET_VIEW_UNSPECIFIED", "ASSET_VIEW_BASIC", "ASSET_VIEW_FULL", "ASSET_VIEW_STANDARD", "ASSET_VIEW_UI", "ASSET_VIEW_LABELS"], "enumDescriptions": ["The asset view is not specified. The API displays the basic view by default.", "The asset view includes only basic metadata of the asset.", "The asset view includes all the metadata of an asset and performance data.", "The asset view includes the standard metadata of an asset.", "The asset view includes fields needed by UI.", "The asset view includes asset name and labels."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/assets", "response": {"$ref": "ListAssetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of an asset.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets/{assetsId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.assets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full name of the asset.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/assets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Asset` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "reportAssetFrames": {"description": "Reports a set of frames.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/assets:reportAssetFrames", "httpMethod": "POST", "id": "migrationcenter.projects.locations.assets.reportAssetFrames", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "source": {"description": "Required. Reference to a source.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/assets:reportAssetFrames", "request": {"$ref": "Frames"}, "response": {"$ref": "ReportAssetFramesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "discoveryClients": {"methods": {"create": {"description": "Creates a new discovery client.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients", "httpMethod": "POST", "id": "migrationcenter.projects.locations.discoveryClients.create", "parameterOrder": ["parent"], "parameters": {"discoveryClientId": {"description": "Required. User specified ID for the discovery client. It will become the last component of the discovery client name. The ID must be unique within the project, is restricted to lower-cased letters and has a maximum length of 63 characters. The ID must match the regular expression: `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/discoveryClients", "request": {"$ref": "DiscoveryClient"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a discovery client.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients/{discoveryClientsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.discoveryClients.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The discovery client name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveryClients/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a discovery client.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients/{discoveryClientsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.discoveryClients.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The discovery client name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveryClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "DiscoveryClient"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the discovery clients in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients", "httpMethod": "GET", "id": "migrationcenter.projects.locations.discoveryClients.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filter expression to filter results by.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. The server may return fewer items than requested. If unspecified, the server will pick an appropriate default value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDiscoveryClients` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDiscoveryClients` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/discoveryClients", "response": {"$ref": "ListDiscoveryClientsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a discovery client.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients/{discoveryClientsId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.discoveryClients.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. Full name of this discovery client.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveryClients/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Update mask is used to specify the fields to be overwritten in the `DiscoveryClient` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "DiscoveryClient"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "sendHeartbeat": {"description": "Sends a discovery client heartbeat. Healthy clients are expected to send heartbeats regularly (normally every few minutes).", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/discoveryClients/{discoveryClientsId}:sendHeartbeat", "httpMethod": "POST", "id": "migrationcenter.projects.locations.discoveryClients.sendHeartbeat", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The discovery client name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/discoveryClients/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:sendHeartbeat", "request": {"$ref": "SendDiscoveryClientHeartbeatRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "groups": {"methods": {"addAssets": {"description": "Adds assets to a group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}:addAssets", "httpMethod": "POST", "id": "migrationcenter.projects.locations.groups.addAssets", "parameterOrder": ["group"], "parameters": {"group": {"description": "Required. Group reference.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+group}:addAssets", "request": {"$ref": "AddAssetsToGroupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new group in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups", "httpMethod": "POST", "id": "migrationcenter.projects.locations.groups.create", "parameterOrder": ["parent"], "parameters": {"groupId": {"description": "Required. User specified ID for the group. It will become the last component of the group name. The ID must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. The ID must match the regular expression: `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/groups", "request": {"$ref": "Group"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.groups.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the group resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.groups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Group"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all groups in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups", "httpMethod": "GET", "id": "migrationcenter.projects.locations.groups.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListGroupsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/groups", "response": {"$ref": "ListGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.groups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The name of the group.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Group` resource by the update. The values specified in the `update_mask` are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Group"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeAssets": {"description": "Removes assets from a group.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/groups/{groupsId}:removeAssets", "httpMethod": "POST", "id": "migrationcenter.projects.locations.groups.removeAssets", "parameterOrder": ["group"], "parameters": {"group": {"description": "Required. Group reference.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/groups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+group}:removeAssets", "request": {"$ref": "RemoveAssetsFromGroupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "importJobs": {"methods": {"create": {"description": "Creates an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs", "httpMethod": "POST", "id": "migrationcenter.projects.locations.importJobs.create", "parameterOrder": ["parent"], "parameters": {"importJobId": {"description": "Required. ID of the import job.", "location": "query", "type": "string"}, "parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/importJobs", "request": {"$ref": "ImportJob"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.importJobs.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to `true`, any `ImportDataFiles` of this job will also be deleted If set to `false`, the request only works if the job has no data files.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.importJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. The level of details of the import job. Default value is FULL.", "enum": ["IMPORT_JOB_VIEW_UNSPECIFIED", "IMPORT_JOB_VIEW_BASIC", "IMPORT_JOB_VIEW_FULL"], "enumDescriptions": ["The import job view is not specified. The API displays the basic view by default.", "The import job view includes basic metadata of an import job. This view does not include payload information.", "The import job view includes all metadata of an import job."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ImportJob"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all import jobs.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs", "httpMethod": "GET", "id": "migrationcenter.projects.locations.importJobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListImportJobsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. The level of details of each import job. Default value is BASIC.", "enum": ["IMPORT_JOB_VIEW_UNSPECIFIED", "IMPORT_JOB_VIEW_BASIC", "IMPORT_JOB_VIEW_FULL"], "enumDescriptions": ["The import job view is not specified. The API displays the basic view by default.", "The import job view includes basic metadata of an import job. This view does not include payload information.", "The import job view includes all metadata of an import job."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/importJobs", "response": {"$ref": "ListImportJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.importJobs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full name of the import job.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Asset` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ImportJob"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "run": {"description": "Runs an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}:run", "httpMethod": "POST", "id": "migrationcenter.projects.locations.importJobs.run", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the import job to run.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:run", "request": {"$ref": "RunImportJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "validate": {"description": "Validates an import job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}:validate", "httpMethod": "POST", "id": "migrationcenter.projects.locations.importJobs.validate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the import job to validate.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:validate", "request": {"$ref": "ValidateImportJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"importDataFiles": {"methods": {"create": {"description": "Creates an import data file.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}/importDataFiles", "httpMethod": "POST", "id": "migrationcenter.projects.locations.importJobs.importDataFiles.create", "parameterOrder": ["parent"], "parameters": {"importDataFileId": {"description": "Required. The ID of the new data file.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the parent of the ImportDataFile.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/importDataFiles", "request": {"$ref": "ImportDataFile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete an import data file.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}/importDataFiles/{importDataFilesId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.importJobs.importDataFiles.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ImportDataFile to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+/importDataFiles/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an import data file.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}/importDataFiles/{importDataFilesId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.importJobs.importDataFiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the ImportDataFile.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+/importDataFiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ImportDataFile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List import data files.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/importJobs/{importJobsId}/importDataFiles", "httpMethod": "GET", "id": "migrationcenter.projects.locations.importJobs.importDataFiles.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of data files to return. The service may return fewer than this value. If unspecified, at most 500 data files will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListImportDataFiles` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListImportDataFiles` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the parent of the `ImportDataFiles` resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/importJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/importDataFiles", "response": {"$ref": "ListImportDataFilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "migrationcenter.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "migrationcenter.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "preferenceSets": {"methods": {"create": {"description": "Creates a new preference set in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/preferenceSets", "httpMethod": "POST", "id": "migrationcenter.projects.locations.preferenceSets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "preferenceSetId": {"description": "Required. User specified ID for the preference set. It will become the last component of the preference set name. The ID must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. The ID must match the regular expression `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/preferenceSets", "request": {"$ref": "PreferenceSet"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a preference set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/preferenceSets/{preferenceSetsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.preferenceSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the group resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/preferenceSets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a preference set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/preferenceSets/{preferenceSetsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.preferenceSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/preferenceSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PreferenceSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the preference sets in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/preferenceSets", "httpMethod": "GET", "id": "migrationcenter.projects.locations.preferenceSets.list", "parameterOrder": ["parent"], "parameters": {"orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, at most 500 preference sets will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListPreferenceSetsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/preferenceSets", "response": {"$ref": "ListPreferenceSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a preference set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/preferenceSets/{preferenceSetsId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.preferenceSets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Name of the preference set.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/preferenceSets/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `PreferenceSet` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "PreferenceSet"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "relations": {"methods": {"get": {"description": "Gets the details of an relation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/relations/{relationsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.relations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/relations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Relation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the relations in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/relations", "httpMethod": "GET", "id": "migrationcenter.projects.locations.relations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListRelationsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/relations", "response": {"$ref": "ListRelationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "reportConfigs": {"methods": {"create": {"description": "Creates a report configuration.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs", "httpMethod": "POST", "id": "migrationcenter.projects.locations.reportConfigs.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "reportConfigId": {"description": "Required. User specified ID for the report config. It will become the last component of the report config name. The ID must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. The ID must match the regular expression: [a-z]([a-z0-9-]{0,61}[a-z0-9])?.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/reportConfigs", "request": {"$ref": "ReportConfig"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a ReportConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.reportConfigs.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to `true`, any child `Reports` of this entity will also be deleted. If set to `false`, the request only works if the resource has no children.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ReportConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.reportConfigs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ReportConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ReportConfigs in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs", "httpMethod": "GET", "id": "migrationcenter.projects.locations.reportConfigs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListReportConfigsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/reportConfigs", "response": {"$ref": "ListReportConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"reports": {"methods": {"create": {"description": "Creates a report.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}/reports", "httpMethod": "POST", "id": "migrationcenter.projects.locations.reportConfigs.reports.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+$", "required": true, "type": "string"}, "reportId": {"description": "Required. User specified id for the report. It will become the last component of the report name. The id must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. The id must match the regular expression: [a-z]([a-z0-9-]{0,61}[a-z0-9])?.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/reports", "request": {"$ref": "Report"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Report.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}/reports/{reportsId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.reportConfigs.reports.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+/reports/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Report.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}/reports/{reportsId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.reportConfigs.reports.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+/reports/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Determines what information to retrieve for the Report.", "enum": ["REPORT_VIEW_UNSPECIFIED", "REPORT_VIEW_BASIC", "REPORT_VIEW_FULL", "REPORT_VIEW_STANDARD"], "enumDescriptions": ["The report view is not specified. The API displays the basic view by default.", "The report view includes only basic metadata of the Report. Useful for list views.", "The report view includes all the metadata of the Report. Useful for preview.", "The report view includes the standard metadata of an report. Useful for detail view."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Report"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Reports in a given ReportConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/reportConfigs/{reportConfigsId}/reports", "httpMethod": "GET", "id": "migrationcenter.projects.locations.reportConfigs.reports.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer items than requested. If unspecified, the server will pick an appropriate default value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results that the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListReportsRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/reportConfigs/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Determines what information to retrieve for each Report.", "enum": ["REPORT_VIEW_UNSPECIFIED", "REPORT_VIEW_BASIC", "REPORT_VIEW_FULL", "REPORT_VIEW_STANDARD"], "enumDescriptions": ["The report view is not specified. The API displays the basic view by default.", "The report view includes only basic metadata of the Report. Useful for list views.", "The report view includes all the metadata of the Report. Useful for preview.", "The report view includes the standard metadata of an report. Useful for detail view."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/reports", "response": {"$ref": "ListReportsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "sources": {"methods": {"create": {"description": "Creates a new source in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources", "httpMethod": "POST", "id": "migrationcenter.projects.locations.sources.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Value for parent.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "sourceId": {"description": "Required. User specified ID for the source. It will become the last component of the source name. The ID must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. The ID must match the regular expression: `[a-z]([a-z0-9-]{0,61}[a-z0-9])?`.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/sources", "request": {"$ref": "Source"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a source.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}", "httpMethod": "DELETE", "id": "migrationcenter.projects.locations.sources.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a source.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.sources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Source"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all the sources in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources", "httpMethod": "GET", "id": "migrationcenter.projects.locations.sources.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Field to sort by. See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer items than requested. If unspecified, the server will pick an appropriate default value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results that the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value for `ListSourcesRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sources", "response": {"$ref": "ListSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a source.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}", "httpMethod": "PATCH", "id": "migrationcenter.projects.locations.sources.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full name of the source.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Source` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Source"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"errorFrames": {"methods": {"get": {"description": "Gets the details of an error frame.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/errorFrames/{errorFramesId}", "httpMethod": "GET", "id": "migrationcenter.projects.locations.sources.errorFrames.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the frame to retrieve. Format: projects/{project}/locations/{location}/sources/{source}/errorFrames/{error_frame}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sources/[^/]+/errorFrames/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. An optional view mode to control the level of details for the frame. The default is a basic frame view.", "enum": ["ERROR_FRAME_VIEW_UNSPECIFIED", "ERROR_FRAME_VIEW_BASIC", "ERROR_FRAME_VIEW_FULL"], "enumDescriptions": ["Value is unset. The system will fallback to the default value.", "Include basic frame data, but not the full contents.", "Include everything."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all error frames in a given source and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sources/{sourcesId}/errorFrames", "httpMethod": "GET", "id": "migrationcenter.projects.locations.sources.errorFrames.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent value (the source) for `ListErrorFramesRequest`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sources/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. An optional view mode to control the level of details of each error frame. The default is a BASIC frame view.", "enum": ["ERROR_FRAME_VIEW_UNSPECIFIED", "ERROR_FRAME_VIEW_BASIC", "ERROR_FRAME_VIEW_FULL"], "enumDescriptions": ["Value is unset. The system will fallback to the default value.", "Include basic frame data, but not the full contents.", "Include everything."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/errorFrames", "response": {"$ref": "ListErrorFramesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250429", "rootUrl": "https://migrationcenter.googleapis.com/", "schemas": {"AddAssetsToGroupRequest": {"description": "A request to add assets to a group.", "id": "AddAssetsToGroupRequest", "properties": {"allowExisting": {"description": "Optional. When this value is set to `false` and one of the given assets is already an existing member of the group, the operation fails with an `Already Exists` error. When set to `true` this situation is silently ignored by the server. Default value is `false`.", "type": "boolean"}, "assets": {"$ref": "AssetList", "description": "Required. List of assets to be added. The maximum number of assets that can be added in a single request is 1000."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "AggregateAssetsValuesRequest": {"description": "A request to aggregate one or more values.", "id": "AggregateAssetsValuesRequest", "properties": {"aggregations": {"description": "Array of aggregations to perform. Up to 25 aggregations can be defined.", "items": {"$ref": "Aggregation"}, "type": "array"}, "filter": {"description": "Optional. The aggregation will be performed on assets that match the provided filter.", "type": "string"}, "showHidden": {"description": "Optional. When this value is set to 'true,' the response will include all assets, including those that are hidden.", "type": "boolean"}}, "type": "object"}, "AggregateAssetsValuesResponse": {"description": "A response to a request to aggregated assets values.", "id": "AggregateAssetsValuesResponse", "properties": {"results": {"description": "The aggregation results.", "items": {"$ref": "AggregationResult"}, "type": "array"}}, "type": "object"}, "Aggregation": {"description": "Message describing an aggregation. The message includes the aggregation type, parameters, and the field on which to perform the aggregation.", "id": "Aggregation", "properties": {"count": {"$ref": "AggregationCount", "description": "Count the number of matching objects."}, "field": {"description": "The name of the field on which to aggregate.", "type": "string"}, "frequency": {"$ref": "AggregationFrequency", "description": "Creates a frequency distribution of all field values."}, "histogram": {"$ref": "AggregationHistogram", "description": "Creates a bucketed histogram of field values."}, "sum": {"$ref": "AggregationSum", "description": "Sum over a numeric field."}}, "type": "object"}, "AggregationCount": {"description": "Object count.", "id": "AggregationCount", "properties": {}, "type": "object"}, "AggregationFrequency": {"description": "Frequency distribution of all field values.", "id": "AggregationFrequency", "properties": {}, "type": "object"}, "AggregationHistogram": {"description": "Histogram of bucketed assets counts by field value.", "id": "AggregationHistogram", "properties": {"lowerBounds": {"description": "Lower bounds of buckets. The response will contain `n+1` buckets for `n` bounds. The first bucket will count all assets for which the field value is smaller than the first bound. Subsequent buckets will count assets for which the field value is greater or equal to a lower bound and smaller than the next one. The last bucket will count assets for which the field value is greater or equal to the final lower bound. You can define up to 20 lower bounds.", "items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "AggregationResult": {"description": "Message describing a result of an aggregation.", "id": "AggregationResult", "properties": {"count": {"$ref": "AggregationResultCount"}, "field": {"type": "string"}, "frequency": {"$ref": "AggregationResultFrequency"}, "histogram": {"$ref": "AggregationResultHistogram"}, "sum": {"$ref": "AggregationResultSum"}}, "type": "object"}, "AggregationResultCount": {"description": "The result of a count aggregation.", "id": "AggregationResultCount", "properties": {"value": {"format": "int64", "type": "string"}}, "type": "object"}, "AggregationResultFrequency": {"description": "The result of a frequency distribution aggregation.", "id": "AggregationResultFrequency", "properties": {"values": {"additionalProperties": {"format": "int64", "type": "string"}, "type": "object"}}, "type": "object"}, "AggregationResultHistogram": {"description": "The result of a bucketed histogram aggregation.", "id": "AggregationResultHistogram", "properties": {"buckets": {"description": "Buckets in the histogram. There will be `n+1` buckets matching `n` lower bounds in the request. The first bucket will be from -infinity to the first bound. Subsequent buckets will be between one bound and the next. The final bucket will be from the final bound to infinity.", "items": {"$ref": "AggregationResultHistogramBucket"}, "type": "array"}}, "type": "object"}, "AggregationResultHistogramBucket": {"description": "A histogram bucket with a lower and upper bound, and a count of items with a field value between those bounds. The lower bound is inclusive and the upper bound is exclusive. Lower bound may be -infinity and upper bound may be infinity.", "id": "AggregationResultHistogramBucket", "properties": {"count": {"description": "Count of items in the bucket.", "format": "int64", "type": "string"}, "lowerBound": {"description": "Lower bound - inclusive.", "format": "double", "type": "number"}, "upperBound": {"description": "Upper bound - exclusive.", "format": "double", "type": "number"}}, "type": "object"}, "AggregationResultSum": {"description": "The result of a sum aggregation.", "id": "AggregationResultSum", "properties": {"value": {"format": "double", "type": "number"}}, "type": "object"}, "AggregationSum": {"description": "Sum of field values.", "id": "AggregationSum", "properties": {}, "type": "object"}, "Asset": {"description": "An asset represents a resource in your environment. Asset types include virtual machines and databases.", "id": "<PERSON><PERSON>", "properties": {"assignedGroups": {"description": "Output only. The list of groups that the asset is assigned to.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "attributes": {"additionalProperties": {"type": "string"}, "description": "Generic asset attributes.", "type": "object"}, "createTime": {"description": "Output only. The timestamp when the asset was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "databaseDeploymentDetails": {"$ref": "DatabaseDeploymentDetails", "description": "Output only. Asset information specific for database deployments.", "readOnly": true}, "databaseDetails": {"$ref": "DatabaseDetails", "description": "Output only. Asset information specific for logical databases.", "readOnly": true}, "hidden": {"description": "Optional. Indicates if the asset is hidden.", "type": "boolean"}, "hideReason": {"description": "Optional. An optional reason for marking this asset as hidden.", "type": "string"}, "hideTime": {"description": "Output only. The timestamp when the asset was marked as hidden.", "format": "google-datetime", "readOnly": true, "type": "string"}, "insightList": {"$ref": "InsightList", "description": "Output only. The list of insights associated with the asset.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "machineDetails": {"$ref": "MachineDetails", "description": "Output only. Asset information specific for virtual and physical machines.", "readOnly": true}, "name": {"description": "Output only. The full name of the asset.", "readOnly": true, "type": "string"}, "performanceData": {"$ref": "AssetPerformanceData", "description": "Output only. Performance data for the asset.", "readOnly": true}, "sources": {"description": "Output only. The list of sources contributing to the asset.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "title": {"description": "Output only. Server generated human readable name of the asset.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the asset was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AssetFrame": {"description": "Contains data reported from an inventory source on an asset.", "id": "AssetFrame", "properties": {"attributes": {"additionalProperties": {"type": "string"}, "description": "Generic asset attributes.", "type": "object"}, "collectionType": {"description": "Optional. Frame collection type, if not specified the collection type will be based on the source type of the source the frame was reported on.", "enum": ["SOURCE_TYPE_UNKNOWN", "SOURCE_TYPE_UPLOAD", "SOURCE_TYPE_GUEST_OS_SCAN", "SOURCE_TYPE_INVENTORY_SCAN", "SOURCE_TYPE_CUSTOM", "SOURCE_TYPE_DISCOVERY_CLIENT"], "enumDescriptions": ["Unspecified", "Manually uploaded file (e.g. CSV)", "Guest-level info", "Inventory-level scan", "Third-party owned sources.", "Discovery clients"], "type": "string"}, "databaseDeploymentDetails": {"$ref": "DatabaseDeploymentDetails", "description": "Asset information specific for database deployments."}, "databaseDetails": {"$ref": "DatabaseDetails", "description": "Asset information specific for logical databases."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "machineDetails": {"$ref": "MachineDetails", "description": "Asset information specific for virtual machines."}, "performanceSamples": {"description": "Asset performance data samples. Samples that are from more than 40 days ago or after tomorrow are ignored.", "items": {"$ref": "PerformanceSample"}, "type": "array"}, "reportTime": {"description": "The time the data was reported.", "format": "google-datetime", "type": "string"}, "traceToken": {"description": "Optional. Trace token is optionally provided to assist with debugging and traceability.", "type": "string"}}, "type": "object"}, "AssetList": {"description": "Lists the asset IDs of all assets.", "id": "AssetList", "properties": {"assetIds": {"description": "Required. A list of asset IDs", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AssetPerformanceData": {"description": "Performance data for an asset.", "id": "AssetPerformanceData", "properties": {"dailyResourceUsageAggregations": {"description": "Daily resource usage aggregations. Contains all of the data available for an asset, up to the last 420 days. Aggregations are sorted from oldest to most recent.", "items": {"$ref": "DailyResourceUsageAggregation"}, "type": "array"}}, "type": "object"}, "AwsEc2PlatformDetails": {"description": "AWS EC2 specific details.", "id": "AwsEc2PlatformDetails", "properties": {"hyperthreading": {"description": "Optional. Whether the machine is hyperthreaded.", "enum": ["HYPERTHREADING_STATUS_UNSPECIFIED", "HYPERTHREADING_STATUS_DISABLED", "HYPERTHREADING_STATUS_ENABLED"], "enumDescriptions": ["Simultaneous Multithreading status unknown.", "Simultaneous Multithreading is disabled or unavailable.", "Simultaneous Multithreading is enabled."], "type": "string"}, "location": {"description": "The location of the machine in the AWS format.", "type": "string"}, "machineTypeLabel": {"description": "AWS platform's machine type label.", "type": "string"}}, "type": "object"}, "AwsRds": {"description": "Specific details for an AWS RDS database deployment.", "id": "AwsRds", "properties": {}, "type": "object"}, "AzureVmPlatformDetails": {"description": "Azure VM specific details.", "id": "AzureVmPlatformDetails", "properties": {"hyperthreading": {"description": "Whether the machine is hyperthreaded.", "enum": ["HYPERTHREADING_STATUS_UNSPECIFIED", "HYPERTHREADING_STATUS_DISABLED", "HYPERTHREADING_STATUS_ENABLED"], "enumDescriptions": ["Simultaneous Multithreading status unknown.", "Simultaneous Multithreading is disabled or unavailable.", "Simultaneous Multithreading is enabled."], "type": "string"}, "location": {"description": "The location of the machine in the Azure format.", "type": "string"}, "machineTypeLabel": {"description": "Azure platform's machine type label.", "type": "string"}, "provisioningState": {"description": "Azure platform's provisioning state.", "type": "string"}}, "type": "object"}, "BatchDeleteAssetsRequest": {"description": "A request to delete a list of asset.", "id": "BatchDeleteAssetsRequest", "properties": {"allowMissing": {"description": "Optional. When this value is set to `true` the request is a no-op for non-existing assets. See https://google.aip.dev/135#delete-if-existing for additional details. Default value is `false`.", "type": "boolean"}, "cascadingRules": {"description": "Optional. Optional cascading rules for deleting related assets.", "items": {"$ref": "CascadingRule"}, "type": "array"}, "names": {"description": "Required. The IDs of the assets to delete. A maximum of 1000 assets can be deleted in a batch. Format: projects/{project}/locations/{location}/assets/{name}.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BatchUpdateAssetsRequest": {"description": "A request to update a list of assets.", "id": "BatchUpdateAssetsRequest", "properties": {"requests": {"description": "Required. The request message specifying the resources to update. A maximum of 1000 assets can be modified in a batch.", "items": {"$ref": "UpdateAssetRequest"}, "type": "array"}}, "type": "object"}, "BatchUpdateAssetsResponse": {"description": "Response for updating a list of assets.", "id": "BatchUpdateAssetsResponse", "properties": {"assets": {"description": "Update asset content. The content only includes values after field mask being applied.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "BiosDetails": {"description": "Details about the BIOS.", "id": "BiosDetails", "properties": {"biosName": {"deprecated": true, "description": "BIOS name. This fields is deprecated. Please use the `id` field instead.", "type": "string"}, "id": {"description": "BIOS ID.", "type": "string"}, "manufacturer": {"description": "BIOS manufacturer.", "type": "string"}, "releaseDate": {"$ref": "Date", "description": "BIOS release date."}, "smbiosUuid": {"description": "SMBIOS UUID.", "type": "string"}, "version": {"description": "BIOS version.", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CascadeLogicalDBsRule": {"description": "Cascading rule for related logical DBs.", "id": "CascadeLogicalDBsRule", "properties": {}, "type": "object"}, "CascadingRule": {"description": "Specifies cascading rules for traversing relations.", "id": "CascadingRule", "properties": {"cascadeLogicalDbs": {"$ref": "CascadeLogicalDBsRule", "description": "Cascading rule for related logical DBs."}}, "type": "object"}, "ComputeEngineMigrationTarget": {"description": "Compute engine migration target.", "id": "ComputeEngineMigrationTarget", "properties": {"shape": {"$ref": "ComputeEngineShapeDescriptor", "description": "Description of the suggested shape for the migration target."}}, "type": "object"}, "ComputeEnginePreferences": {"description": "The user preferences relating to Compute Engine target platform.", "id": "ComputeEnginePreferences", "properties": {"licenseType": {"description": "License type to consider when calculating costs for virtual machine insights and recommendations. If unspecified, costs are calculated based on the default licensing plan.", "enum": ["LICENSE_TYPE_UNSPECIFIED", "LICENSE_TYPE_DEFAULT", "LICENSE_TYPE_BRING_YOUR_OWN_LICENSE"], "enumDescriptions": ["Unspecified (default value).", "Default Google Cloud licensing plan. Licensing is charged per usage. This a good value to start with.", "Bring-your-own-license (BYOL) plan. User provides the OS license."], "type": "string"}, "machinePreferences": {"$ref": "MachinePreferences", "description": "Preferences concerning the machine types to consider on Compute Engine."}, "persistentDiskType": {"description": "Persistent disk type to use. If unspecified (default), all types are considered, based on available usage data.", "enum": ["PERSISTENT_DISK_TYPE_UNSPECIFIED", "PERSISTENT_DISK_TYPE_STANDARD", "PERSISTENT_DISK_TYPE_BALANCED", "PERSISTENT_DISK_TYPE_SSD"], "enumDescriptions": ["Unspecified. Fallback to default value based on context.", "Standard HDD Persistent Disk.", "Balanced Persistent <PERSON>.", "SSD Persistent Disk."], "type": "string"}}, "type": "object"}, "ComputeEngineShapeDescriptor": {"description": "Compute Engine target shape descriptor.", "id": "ComputeEngineShapeDescriptor", "properties": {"logicalCoreCount": {"description": "Output only. Number of logical cores.", "format": "int32", "readOnly": true, "type": "integer"}, "machineType": {"description": "Output only. Compute Engine machine type.", "readOnly": true, "type": "string"}, "memoryMb": {"description": "Memory in mebibytes.", "format": "int32", "type": "integer"}, "physicalCoreCount": {"description": "Number of physical cores.", "format": "int32", "type": "integer"}, "series": {"description": "Output only. Compute Engine machine series.", "readOnly": true, "type": "string"}, "storage": {"description": "Output only. Compute Engine storage. Never empty.", "items": {"$ref": "ComputeStorageDescriptor"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ComputeStorageDescriptor": {"description": "Compute Engine storage option descriptor.", "id": "ComputeStorageDescriptor", "properties": {"sizeGb": {"description": "Output only. Disk size in GiB.", "format": "int32", "readOnly": true, "type": "integer"}, "type": {"description": "Output only. Disk type backing the storage.", "enum": ["PERSISTENT_DISK_TYPE_UNSPECIFIED", "PERSISTENT_DISK_TYPE_STANDARD", "PERSISTENT_DISK_TYPE_BALANCED", "PERSISTENT_DISK_TYPE_SSD"], "enumDescriptions": ["Unspecified. Fallback to default value based on context.", "Standard HDD Persistent Disk.", "Balanced Persistent <PERSON>.", "SSD Persistent Disk."], "readOnly": true, "type": "string"}}, "type": "object"}, "CpuUsageSample": {"description": "CPU usage sample.", "id": "CpuUsageSample", "properties": {"utilizedPercentage": {"description": "Percentage of total CPU capacity utilized. Must be in the interval [0, 100]. On most systems can be calculated using 100 - idle percentage.", "format": "float", "type": "number"}}, "type": "object"}, "DailyResourceUsageAggregation": {"description": "Usage data aggregation for a single day.", "id": "DailyResourceUsageAggregation", "properties": {"cpu": {"$ref": "DailyResourceUsageAggregationCPU", "description": "CPU usage."}, "date": {"$ref": "Date", "description": "Aggregation date. Day boundaries are at midnight UTC."}, "disk": {"$ref": "DailyResourceUsageAggregationDisk", "description": "Disk usage."}, "memory": {"$ref": "DailyResourceUsageAggregationMemory", "description": "Memory usage."}, "network": {"$ref": "DailyResourceUsageAggregationNetwork", "description": "Network usage."}}, "type": "object"}, "DailyResourceUsageAggregationCPU": {"description": "Statistical aggregation of CPU usage.", "id": "DailyResourceUsageAggregationCPU", "properties": {"utilizationPercentage": {"$ref": "DailyResourceUsageAggregationStats", "description": "CPU utilization percentage."}}, "type": "object"}, "DailyResourceUsageAggregationDisk": {"description": "Statistical aggregation of disk usage.", "id": "DailyResourceUsageAggregationDisk", "properties": {"iops": {"$ref": "DailyResourceUsageAggregationStats", "description": "Optional. Disk I/O operations per second."}, "readIops": {"$ref": "DailyResourceUsageAggregationStats", "description": "Optional. Disk read I/O operations per second."}, "writeIops": {"$ref": "DailyResourceUsageAggregationStats", "description": "Optional. Disk write I/O operations per second."}}, "type": "object"}, "DailyResourceUsageAggregationMemory": {"description": "Statistical aggregation of memory usage.", "id": "DailyResourceUsageAggregationMemory", "properties": {"utilizationPercentage": {"$ref": "DailyResourceUsageAggregationStats", "description": "Memory utilization percentage."}}, "type": "object"}, "DailyResourceUsageAggregationNetwork": {"description": "Statistical aggregation of network usage.", "id": "DailyResourceUsageAggregationNetwork", "properties": {"egressBps": {"$ref": "DailyResourceUsageAggregationStats", "description": "Network egress in B/s."}, "ingressBps": {"$ref": "DailyResourceUsageAggregationStats", "description": "Network ingress in B/s."}}, "type": "object"}, "DailyResourceUsageAggregationStats": {"description": "Statistical aggregation of samples for a single resource usage.", "id": "DailyResourceUsageAggregationStats", "properties": {"average": {"description": "Average usage value.", "format": "float", "type": "number"}, "median": {"description": "Median usage value.", "format": "float", "type": "number"}, "ninteyFifthPercentile": {"description": "95th percentile usage value.", "format": "float", "type": "number"}, "peak": {"description": "Peak usage value.", "format": "float", "type": "number"}}, "type": "object"}, "DatabaseDeploymentDetails": {"description": "The details of a database deployment asset.", "id": "DatabaseDeploymentDetails", "properties": {"aggregatedStats": {"$ref": "DatabaseDeploymentDetailsAggregatedStats", "description": "Output only. Aggregated stats for the database deployment.", "readOnly": true}, "awsRds": {"$ref": "AwsRds", "description": "Optional. Details of an AWS RDS instance."}, "edition": {"description": "Optional. The database deployment edition.", "type": "string"}, "generatedId": {"description": "Optional. The database deployment generated ID.", "type": "string"}, "manualUniqueId": {"description": "Optional. A manual unique ID set by the user.", "type": "string"}, "mysql": {"$ref": "MysqlDatabaseDeployment", "description": "Optional. Details of a MYSQL database deployment."}, "postgresql": {"$ref": "PostgreSqlDatabaseDeployment", "description": "Optional. Details of a PostgreSQL database deployment."}, "sqlServer": {"$ref": "SqlServerDatabaseDeployment", "description": "Optional. Details of a Microsoft SQL Server database deployment."}, "topology": {"$ref": "DatabaseDeploymentTopology", "description": "Optional. Details of the database deployment topology."}, "version": {"description": "Optional. The database deployment version.", "type": "string"}}, "type": "object"}, "DatabaseDeploymentDetailsAggregatedStats": {"description": "Aggregated stats for the database deployment.", "id": "DatabaseDeploymentDetailsAggregatedStats", "properties": {"databaseCount": {"description": "Output only. The number of databases in the deployment.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "DatabaseDeploymentTopology": {"description": "Details of database deployment's topology.", "id": "DatabaseDeploymentTopology", "properties": {"coreCount": {"description": "Optional. Number of total logical cores.", "format": "int32", "type": "integer"}, "coreLimit": {"description": "Optional. Number of total logical cores limited by db deployment.", "format": "int32", "type": "integer"}, "diskAllocatedBytes": {"description": "Optional. Disk allocated in bytes.", "format": "int64", "type": "string"}, "diskUsedBytes": {"description": "Optional. Disk used in bytes.", "format": "int64", "type": "string"}, "instances": {"description": "Optional. List of database instances.", "items": {"$ref": "DatabaseInstance"}, "type": "array"}, "memoryBytes": {"description": "Optional. Total memory in bytes.", "format": "int64", "type": "string"}, "memoryLimitBytes": {"description": "Optional. Total memory in bytes limited by db deployment.", "format": "int64", "type": "string"}, "physicalCoreCount": {"description": "Optional. Number of total physical cores.", "format": "int32", "type": "integer"}, "physicalCoreLimit": {"description": "Optional. Number of total physical cores limited by db deployment.", "format": "int32", "type": "integer"}}, "type": "object"}, "DatabaseDetails": {"description": "Details of a logical database.", "id": "DatabaseDetails", "properties": {"allocatedStorageBytes": {"description": "Optional. The allocated storage for the database in bytes.", "format": "int64", "type": "string"}, "databaseName": {"description": "Required. The name of the database.", "type": "string"}, "parentDatabaseDeployment": {"$ref": "DatabaseDetailsParentDatabaseDeployment", "description": "Required. The parent database deployment that contains the logical database."}, "schemas": {"description": "Optional. The database schemas.", "items": {"$ref": "DatabaseSchema"}, "type": "array"}}, "type": "object"}, "DatabaseDetailsParentDatabaseDeployment": {"description": "The identifiers of the parent database deployment.", "id": "DatabaseDetailsParentDatabaseDeployment", "properties": {"generatedId": {"description": "Optional. The parent database deployment generated ID.", "type": "string"}, "manualUniqueId": {"description": "Optional. The parent database deployment optional manual unique ID set by the user.", "type": "string"}}, "type": "object"}, "DatabaseInstance": {"description": "Details of a database instance.", "id": "DatabaseInstance", "properties": {"instanceName": {"description": "Optional. The instance's name.", "type": "string"}, "network": {"$ref": "DatabaseInstanceNetwork", "description": "Optional. Networking details."}, "role": {"description": "Optional. The instance role in the database engine.", "enum": ["ROLE_UNSPECIFIED", "PRIMARY", "SECONDARY", "ARBITER"], "enumDescriptions": ["Unspecified.", "Primary.", "Secondary.", "Arbiter."], "type": "string"}}, "type": "object"}, "DatabaseInstanceNetwork": {"description": "Network details of a database instance.", "id": "DatabaseInstanceNetwork", "properties": {"hostNames": {"description": "Optional. The instance's host names.", "items": {"type": "string"}, "type": "array"}, "ipAddresses": {"description": "Optional. The instance's IP addresses.", "items": {"type": "string"}, "type": "array"}, "primaryMacAddress": {"description": "Optional. The instance's primary MAC address.", "type": "string"}}, "type": "object"}, "DatabaseObjects": {"description": "Details of a group of database objects.", "id": "DatabaseObjects", "properties": {"category": {"description": "Optional. The category of the objects.", "enum": ["CATEGORY_UNSPECIFIED", "TABLE", "INDEX", "CONSTRAINTS", "VIEWS", "SOURCE_CODE", "OTHER"], "enumDescriptions": ["Unspecified type.", "Table.", "Index.", "Constraints.", "Views.", "Source code, e.g. procedures.", "Uncategorized objects."], "type": "string"}, "count": {"description": "Optional. The number of objects.", "format": "int64", "type": "string"}}, "type": "object"}, "DatabaseSchema": {"description": "Details of a database schema.", "id": "DatabaseSchema", "properties": {"mysql": {"$ref": "MySqlSchemaDetails", "description": "Optional. Details of a Mysql schema."}, "objects": {"description": "Optional. List of details of objects by category.", "items": {"$ref": "DatabaseObjects"}, "type": "array"}, "postgresql": {"$ref": "PostgreSqlSchemaDetails", "description": "Optional. Details of a PostgreSql schema."}, "schemaName": {"description": "Required. The name of the schema.", "type": "string"}, "sqlServer": {"$ref": "SqlServerSchemaDetails", "description": "Optional. Details of a SqlServer schema."}, "tablesSizeBytes": {"description": "Optional. The total size of tables in bytes.", "format": "int64", "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DiscoveryClient": {"description": "Represents an installed Migration Center Discovery Client instance.", "id": "DiscoveryClient", "properties": {"createTime": {"description": "Output only. Time when the discovery client was first created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Free text description. Maximum length is 1000 characters.", "type": "string"}, "displayName": {"description": "Optional. Free text display name. Maximum length is 63 characters.", "type": "string"}, "errors": {"description": "Output only. Errors affecting client functionality.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "expireTime": {"description": "Optional. Client expiration time in UTC. If specified, the backend will not accept new frames after this time.", "format": "google-datetime", "type": "string"}, "heartbeatTime": {"description": "Output only. Last heartbeat time. Healthy clients are expected to send heartbeats regularly (normally every few minutes).", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs.", "type": "object"}, "name": {"description": "Output only. Identifier. Full name of this discovery client.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Required. Service account used by the discovery client for various operation.", "type": "string"}, "signalsEndpoint": {"description": "Output only. This field is intended for internal use.", "readOnly": true, "type": "string"}, "source": {"description": "Required. Immutable. Full name of the source object associated with this discovery client.", "type": "string"}, "state": {"description": "Output only. Current state of the discovery client.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "OFFLINE", "DEGRADED", "EXPIRED"], "enumDescriptions": ["Client state is unspecified.", "Client is active.", "Client is offline.", "Client is in a degraded state. See the `errors` field for details.", "Client has expired. See the expire_time field for the expire time."], "readOnly": true, "type": "string"}, "ttl": {"description": "Optional. Input only. Client time-to-live. If specified, the backend will not accept new frames after this time. This field is input only. The derived expiration time is provided as output through the `expire_time` field.", "format": "google-duration", "type": "string"}, "updateTime": {"description": "Output only. Time when the discovery client was last updated. This value is not updated by heartbeats, to view the last heartbeat time please refer to the `heartbeat_time` field.", "format": "google-datetime", "readOnly": true, "type": "string"}, "version": {"description": "Output only. Client version, as reported in recent heartbeat.", "readOnly": true, "type": "string"}}, "type": "object"}, "DiskEntry": {"description": "Single disk entry.", "id": "DiskEntry", "properties": {"capacityBytes": {"description": "Disk capacity.", "format": "int64", "type": "string"}, "diskLabel": {"description": "Disk label.", "type": "string"}, "diskLabelType": {"description": "Disk label type (e.g. BIOS/GPT)", "type": "string"}, "freeBytes": {"description": "Disk free space.", "format": "int64", "type": "string"}, "hwAddress": {"description": "Disk hardware address (e.g. 0:1 for SCSI).", "type": "string"}, "interfaceType": {"description": "Disks interface type.", "enum": ["INTERFACE_TYPE_UNSPECIFIED", "IDE", "SATA", "SAS", "SCSI", "NVME", "FC", "ISCSI"], "enumDescriptions": ["Interface type unknown or unspecified.", "IDE interface type.", "SATA interface type.", "SAS interface type.", "SCSI interface type.", "NVME interface type.", "FC interface type.", "iSCSI interface type."], "type": "string"}, "partitions": {"$ref": "DiskPartitionList", "description": "Partition layout."}, "vmware": {"$ref": "VmwareDiskConfig", "description": "VMware disk details."}}, "type": "object"}, "DiskEntryList": {"description": "VM disks.", "id": "DiskEntryList", "properties": {"entries": {"description": "Disk entries.", "items": {"$ref": "DiskEntry"}, "type": "array"}}, "type": "object"}, "DiskPartition": {"description": "Disk Partition details.", "id": "DiskPartition", "properties": {"capacityBytes": {"description": "Partition capacity.", "format": "int64", "type": "string"}, "fileSystem": {"description": "Partition file system.", "type": "string"}, "freeBytes": {"description": "Partition free space.", "format": "int64", "type": "string"}, "mountPoint": {"description": "Mount point (Linux/Windows) or drive letter (Windows).", "type": "string"}, "subPartitions": {"$ref": "DiskPartitionList", "description": "Sub-partitions."}, "type": {"description": "Partition type.", "type": "string"}, "uuid": {"description": "Partition UUID.", "type": "string"}}, "type": "object"}, "DiskPartitionDetails": {"description": "Disk partition details.", "id": "DiskPartitionDetails", "properties": {"freeSpaceBytes": {"description": "Output only. Total free space of all partitions.", "format": "int64", "readOnly": true, "type": "string"}, "partitions": {"$ref": "DiskPartitionList", "description": "Optional. List of partitions."}, "totalCapacityBytes": {"description": "Output only. Total capacity of all partitions.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "DiskPartitionList": {"description": "Disk partition list.", "id": "DiskPartitionList", "properties": {"entries": {"description": "Partition entries.", "items": {"$ref": "DiskPartition"}, "type": "array"}}, "type": "object"}, "DiskUsageSample": {"description": "Disk usage sample. Values are across all disks.", "id": "DiskUsageSample", "properties": {"averageIops": {"description": "Optional. Average IOPS sampled over a short window. Must be non-negative. If read or write are set, the sum of read and write will override the value of the average_iops.", "format": "float", "type": "number"}, "averageReadIops": {"description": "Optional. Average read IOPS sampled over a short window. Must be non-negative. If both read and write are zero they are ignored.", "format": "float", "type": "number"}, "averageWriteIops": {"description": "Optional. Average write IOPS sampled over a short window. Must be non-negative. If both read and write are zero they are ignored.", "format": "float", "type": "number"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ErrorFrame": {"description": "Message representing a frame which failed to be processed due to an error.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"ingestionTime": {"description": "Output only. Frame ingestion time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The identifier of the ErrorFrame.", "readOnly": true, "type": "string"}, "originalFrame": {"$ref": "AssetFrame", "description": "Output only. The frame that was originally reported.", "readOnly": true}, "violations": {"description": "Output only. All the violations that were detected for the frame.", "items": {"$ref": "FrameViolationEntry"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ExecutionReport": {"description": "A resource that reports result of the import job execution.", "id": "ExecutionReport", "properties": {"executionErrors": {"$ref": "ValidationReport", "description": "Validation errors encountered during the execution of the import job."}, "framesReported": {"description": "Total number of asset frames reported for the import job.", "format": "int32", "type": "integer"}, "totalRowsCount": {"description": "Output only. Total number of rows in the import job.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "FileValidationReport": {"description": "A resource that aggregates the validation errors found in an import job file.", "id": "FileValidationReport", "properties": {"fileErrors": {"description": "List of file level errors.", "items": {"$ref": "ImportError"}, "type": "array"}, "fileName": {"description": "The name of the file.", "type": "string"}, "partialReport": {"description": "Flag indicating that processing was aborted due to maximum number of errors.", "type": "boolean"}, "rowErrors": {"description": "Partial list of rows that encountered validation error.", "items": {"$ref": "ImportRowError"}, "type": "array"}}, "type": "object"}, "FitDescriptor": {"description": "Describes the fit level of an asset for migration to a specific target.", "id": "FitDescriptor", "properties": {"fitLevel": {"description": "Output only. Fit level.", "enum": ["FIT_LEVEL_UNSPECIFIED", "FIT", "NO_FIT", "REQUIRES_EFFORT"], "enumDescriptions": ["Not enough information.", "Fit.", "No Fit.", "Fit with effort."], "readOnly": true, "type": "string"}}, "type": "object"}, "FrameViolationEntry": {"description": "A resource that contains a single violation of a reported `AssetFrame` resource.", "id": "FrameViolationEntry", "properties": {"field": {"description": "The field of the original frame where the violation occurred.", "type": "string"}, "violation": {"description": "A message describing the violation.", "type": "string"}}, "type": "object"}, "Frames": {"description": "Collection of frame data.", "id": "Frames", "properties": {"framesData": {"description": "A repeated field of asset data.", "items": {"$ref": "AssetFrame"}, "type": "array"}}, "type": "object"}, "FstabEntry": {"description": "Single fstab entry.", "id": "FstabEntry", "properties": {"file": {"description": "The mount point for the filesystem.", "type": "string"}, "freq": {"description": "Used by dump to determine which filesystems need to be dumped.", "format": "int32", "type": "integer"}, "mntops": {"description": "Mount options associated with the filesystem.", "type": "string"}, "passno": {"description": "Used by the fsck(8) program to determine the order in which filesystem checks are done at reboot time.", "format": "int32", "type": "integer"}, "spec": {"description": "The block special device or remote filesystem to be mounted.", "type": "string"}, "vfstype": {"description": "The type of the filesystem.", "type": "string"}}, "type": "object"}, "FstabEntryList": {"description": "Fstab content.", "id": "FstabEntryList", "properties": {"entries": {"description": "Fstab entries.", "items": {"$ref": "FstabEntry"}, "type": "array"}}, "type": "object"}, "GenericInsight": {"description": "A generic insight about an asset.", "id": "GenericInsight", "properties": {"additionalInformation": {"description": "Output only. Additional information about the insight, each entry can be a logical entry and must make sense if it is displayed with line breaks between each entry. Text can contain md style links.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "defaultMessage": {"description": "Output only. In case message_code is not yet known by the client default_message will be the message to be used instead.", "readOnly": true, "type": "string"}, "messageId": {"description": "Output only. Represents a globally unique message id for this insight, can be used for localization purposes, in case message_code is not yet known by the client use default_message instead.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GenericPlatformDetails": {"description": "Generic platform details.", "id": "GenericPlatformDetails", "properties": {"hyperthreading": {"description": "Whether the machine is hyperthreaded.", "enum": ["HYPERTHREADING_STATUS_UNSPECIFIED", "HYPERTHREADING_STATUS_DISABLED", "HYPERTHREADING_STATUS_ENABLED"], "enumDescriptions": ["Simultaneous Multithreading status unknown.", "Simultaneous Multithreading is disabled or unavailable.", "Simultaneous Multithreading is enabled."], "type": "string"}, "location": {"description": "Free text representation of the machine location. The format of this field should not be relied on. Different VMs in the same location may have different string values for this field.", "type": "string"}}, "type": "object"}, "Group": {"description": "A resource that represents an asset group. The purpose of an asset group is to bundle a set of assets that have something in common, while allowing users to add annotations to the group. An asset can belong to multiple groups.", "id": "Group", "properties": {"createTime": {"description": "Output only. The timestamp when the group was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. The description of the group.", "type": "string"}, "displayName": {"description": "Optional. User-friendly display name.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "name": {"description": "Output only. The name of the group.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the group was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GuestConfigDetails": {"description": "Guest OS config information.", "id": "GuestConfigDetails", "properties": {"fstab": {"$ref": "FstabEntryList", "description": "Mount list (Linux fstab)."}, "hosts": {"$ref": "HostsEntryList", "description": "Hosts file (/etc/hosts)."}, "issue": {"description": "OS issue (typically /etc/issue in Linux).", "type": "string"}, "nfsExports": {"$ref": "NfsExportList", "description": "NFS exports."}, "selinuxMode": {"description": "Security-Enhanced Linux (SELinux) mode.", "enum": ["SE_LINUX_MODE_UNSPECIFIED", "SE_LINUX_MODE_DISABLED", "SE_LINUX_MODE_PERMISSIVE", "SE_LINUX_MODE_ENFORCING"], "enumDescriptions": ["SELinux mode unknown or unspecified.", "<PERSON><PERSON><PERSON><PERSON> is disabled.", "SELinux permissive mode.", "SELinux enforcing mode."], "type": "string"}}, "type": "object"}, "GuestInstalledApplication": {"description": "Guest installed application information.", "id": "GuestInstalledApplication", "properties": {"applicationName": {"description": "Installed application name.", "type": "string"}, "installTime": {"description": "The time when the application was installed.", "format": "google-datetime", "type": "string"}, "licenses": {"description": "License strings associated with the installed application.", "items": {"type": "string"}, "type": "array"}, "path": {"description": "Source path.", "type": "string"}, "vendor": {"description": "Installed application vendor.", "type": "string"}, "version": {"description": "Installed application version.", "type": "string"}}, "type": "object"}, "GuestInstalledApplicationList": {"description": "Guest installed application list.", "id": "GuestInstalledApplicationList", "properties": {"entries": {"description": "Application entries.", "items": {"$ref": "GuestInstalledApplication"}, "type": "array"}}, "type": "object"}, "GuestOsDetails": {"description": "Information from Guest-level collections.", "id": "GuestOsDetails", "properties": {"config": {"$ref": "GuestConfigDetails", "description": "OS and app configuration."}, "family": {"description": "What family the OS belong to, if known.", "enum": ["OS_FAMILY_UNKNOWN", "OS_FAMILY_WINDOWS", "OS_FAMILY_LINUX", "OS_FAMILY_UNIX"], "enumDescriptions": ["", "Microsoft Windows Server and Desktop.", "Various Linux flavors.", "Non-Linux Unix flavors."], "type": "string"}, "osName": {"description": "The name of the operating system.", "type": "string"}, "runtime": {"$ref": "GuestRuntimeDetails", "description": "Runtime information."}, "version": {"description": "The version of the operating system.", "type": "string"}}, "type": "object"}, "GuestRuntimeDetails": {"description": "Guest OS runtime information.", "id": "GuestRuntimeDetails", "properties": {"domain": {"description": "Domain, e.g. c.stratozone-development.internal.", "type": "string"}, "installedApps": {"$ref": "GuestInstalledApplicationList", "description": "Installed applications information."}, "lastBootTime": {"description": "Last time the OS was booted.", "format": "google-datetime", "type": "string"}, "machineName": {"description": "Machine name.", "type": "string"}, "network": {"$ref": "RuntimeNetworkInfo", "description": "Runtime network information (connections, ports)."}, "openFileList": {"$ref": "OpenFileList", "description": "Open files information."}, "processes": {"$ref": "RunningProcessList", "description": "Running processes."}, "services": {"$ref": "RunningServiceList", "description": "Running background services."}}, "type": "object"}, "HostsEntry": {"description": "Single /etc/hosts entry.", "id": "HostsEntry", "properties": {"hostNames": {"description": "List of host names / aliases.", "items": {"type": "string"}, "type": "array"}, "ip": {"description": "IP (raw, IPv4/6 agnostic).", "type": "string"}}, "type": "object"}, "HostsEntryList": {"description": "Hosts content.", "id": "HostsEntryList", "properties": {"entries": {"description": "Hosts entries.", "items": {"$ref": "HostsEntry"}, "type": "array"}}, "type": "object"}, "ImportDataFile": {"description": "A resource that represents a payload file in an import job.", "id": "ImportDataFile", "properties": {"createTime": {"description": "Output only. The timestamp when the file was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "User-friendly display name. Maximum length is 63 characters.", "type": "string"}, "format": {"description": "Required. The payload format.", "enum": ["IMPORT_JOB_FORMAT_UNSPECIFIED", "IMPORT_JOB_FORMAT_RVTOOLS_XLSX", "IMPORT_JOB_FORMAT_RVTOOLS_CSV", "IMPORT_JOB_FORMAT_EXPORTED_AWS_CSV", "IMPORT_JOB_FORMAT_EXPORTED_AZURE_CSV", "IMPORT_JOB_FORMAT_STRATOZONE_CSV", "IMPORT_JOB_FORMAT_DATABASE_ZIP"], "enumDescriptions": ["Default value.", "RVTools format (XLSX).", "RVTools format (CSV).", "CSV format exported from AWS using the AWS collection script.", "CSV format exported from Azure using the Azure collection script.", "CSV format created manually and following the StratoZone format. For more information, see Manually create and upload data tables.", "ZIP file with nested CSV files generated by a database collector."], "type": "string"}, "name": {"description": "Output only. The name of the file.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the import data file.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE"], "enumDescriptions": ["Default value.", "The data file is being created.", "The data file completed initialization."], "readOnly": true, "type": "string"}, "uploadFileInfo": {"$ref": "UploadFileInfo", "description": "Information about a file that is uploaded to a storage service."}}, "type": "object"}, "ImportError": {"description": "A resource that reports the errors encountered while processing an import job.", "id": "ImportError", "properties": {"errorDetails": {"description": "The error information.", "type": "string"}, "severity": {"description": "The severity of the error.", "enum": ["SEVERITY_UNSPECIFIED", "ERROR", "WARNING", "INFO"], "enumDescriptions": ["", "", "", ""], "type": "string"}}, "type": "object"}, "ImportJob": {"description": "A resource that represents the background job that imports asset frames.", "id": "ImportJob", "properties": {"assetSource": {"description": "Required. Reference to a source.", "type": "string"}, "completeTime": {"description": "Output only. The timestamp when the import job was completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The timestamp when the import job was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. User-friendly display name. Maximum length is 256 characters.", "type": "string"}, "executionReport": {"$ref": "ExecutionReport", "description": "Output only. The report with the results of running the import job.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels as key value pairs.", "type": "object"}, "name": {"description": "Output only. The full name of the import job.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the import job.", "enum": ["IMPORT_JOB_STATE_UNSPECIFIED", "IMPORT_JOB_STATE_PENDING", "IMPORT_JOB_STATE_RUNNING", "IMPORT_JOB_STATE_COMPLETED", "IMPORT_JOB_STATE_FAILED", "IMPORT_JOB_STATE_VALIDATING", "IMPORT_JOB_STATE_FAILED_VALIDATION", "IMPORT_JOB_STATE_READY"], "enumDescriptions": ["Default value.", "The import job is pending.", "The processing of the import job is ongoing.", "The import job processing has completed.", "The import job failed to be processed.", "The import job is being validated.", "The import job contains blocking errors.", "The validation of the job completed with no blocking errors."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the import job was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "validationReport": {"$ref": "ValidationReport", "description": "Output only. The report with the validation results of the import job.", "readOnly": true}}, "type": "object"}, "ImportRowError": {"description": "A resource that reports the import job errors at row level.", "id": "ImportRowError", "properties": {"archiveError": {"$ref": "ImportRowErrorArchiveErrorDetails", "description": "Error details for an archive file."}, "assetTitle": {"description": "Output only. The asset title.", "readOnly": true, "type": "string"}, "csvError": {"$ref": "ImportRowErrorCsvErrorDetails", "description": "Error details for a CSV file."}, "errors": {"description": "The list of errors detected in the row.", "items": {"$ref": "ImportError"}, "type": "array"}, "rowNumber": {"deprecated": true, "description": "The row number where the error was detected.", "format": "int32", "type": "integer"}, "vmName": {"description": "The name of the VM in the row.", "type": "string"}, "vmUuid": {"description": "The VM UUID.", "type": "string"}, "xlsxError": {"$ref": "ImportRowErrorXlsxErrorDetails", "description": "Error details for an XLSX file."}}, "type": "object"}, "ImportRowErrorArchiveErrorDetails": {"description": "Error details for an archive file.", "id": "ImportRowErrorArchiveErrorDetails", "properties": {"csvError": {"$ref": "ImportRowErrorCsvErrorDetails", "description": "Error details for a CSV file."}, "filePath": {"description": "Output only. The file path inside the archive where the error was detected.", "readOnly": true, "type": "string"}}, "type": "object"}, "ImportRowErrorCsvErrorDetails": {"description": "Error details for a CSV file.", "id": "ImportRowErrorCsvErrorDetails", "properties": {"rowNumber": {"description": "The row number where the error was detected.", "format": "int32", "type": "integer"}}, "type": "object"}, "ImportRowErrorXlsxErrorDetails": {"description": "Error details for an XLSX file.", "id": "ImportRowErrorXlsxErrorDetails", "properties": {"rowNumber": {"description": "The row number where the error was detected.", "format": "int32", "type": "integer"}, "sheet": {"description": "The name of the sheet where the error was detected.", "type": "string"}}, "type": "object"}, "Insight": {"description": "An insight about an asset.", "id": "Insight", "properties": {"genericInsight": {"$ref": "GenericInsight", "description": "Output only. A generic insight about an asset.", "readOnly": true}, "migrationInsight": {"$ref": "MigrationInsight", "description": "Output only. An insight about potential migrations for an asset.", "readOnly": true}}, "type": "object"}, "InsightList": {"description": "Message containing insights list.", "id": "InsightList", "properties": {"insights": {"description": "Output only. Insights of the list.", "items": {"$ref": "Insight"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. Update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ListAssetsResponse": {"description": "Response message for listing assets.", "id": "ListAssetsResponse", "properties": {"assets": {"description": "A list of assets.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDiscoveryClientsResponse": {"description": "Response message for listing discovery clients.", "id": "ListDiscoveryClientsResponse", "properties": {"discoveryClients": {"description": "List of discovery clients.", "items": {"$ref": "DiscoveryClient"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListErrorFramesResponse": {"description": "A response for listing error frames.", "id": "ListErrorFramesResponse", "properties": {"errorFrames": {"description": "The list of error frames.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGroupsResponse": {"description": "A response for listing groups.", "id": "ListGroupsResponse", "properties": {"groups": {"description": "The list of Group", "items": {"$ref": "Group"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListImportDataFilesResponse": {"description": "Response for listing payload files of an import job.", "id": "ListImportDataFilesResponse", "properties": {"importDataFiles": {"description": "The list of import data files.", "items": {"$ref": "ImportDataFile"}, "type": "array"}, "nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListImportJobsResponse": {"description": "A response for listing import jobs.", "id": "ListImportJobsResponse", "properties": {"importJobs": {"description": "The list of import jobs.", "items": {"$ref": "ImportJob"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPreferenceSetsResponse": {"description": "Response message for listing preference sets.", "id": "ListPreferenceSetsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "preferenceSets": {"description": "The list of PreferenceSets", "items": {"$ref": "PreferenceSet"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRelationsResponse": {"description": "Response message for listing relations.", "id": "ListRelationsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "relations": {"description": "A list of relations.", "items": {"$ref": "Relation"}, "type": "array"}}, "type": "object"}, "ListReportConfigsResponse": {"description": "Response message for listing report configs.", "id": "ListReportConfigsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "reportConfigs": {"description": "A list of report configs.", "items": {"$ref": "ReportConfig"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListReportsResponse": {"description": "Response message for listing Reports.", "id": "ListReportsResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "reports": {"description": "The list of Reports.", "items": {"$ref": "Report"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListSourcesResponse": {"description": "Response message for listing sources.", "id": "ListSourcesResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return.", "type": "string"}, "sources": {"description": "The list of sources.", "items": {"$ref": "Source"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "MachineArchitectureDetails": {"description": "Details of the machine architecture.", "id": "MachineArchitectureDetails", "properties": {"bios": {"$ref": "BiosDetails", "description": "BIOS Details."}, "cpuArchitecture": {"description": "CPU architecture, e.g., \"x64-based PC\", \"x86_64\", \"i686\" etc.", "type": "string"}, "cpuManufacturer": {"description": "Optional. CPU manufacturer, e.g., \"Intel\", \"AMD\".", "type": "string"}, "cpuName": {"description": "CPU name, e.g., \"Intel Xeon E5-2690\", \"AMD EPYC 7571\" etc.", "type": "string"}, "cpuSocketCount": {"description": "Number of processor sockets allocated to the machine.", "format": "int32", "type": "integer"}, "cpuThreadCount": {"deprecated": true, "description": "Deprecated: use MachineDetails.core_count instead. Number of CPU threads allocated to the machine.", "format": "int32", "type": "integer"}, "firmwareType": {"description": "Firmware type.", "enum": ["FIRMWARE_TYPE_UNSPECIFIED", "BIOS", "EFI"], "enumDescriptions": ["Unspecified or unknown.", "BIOS firmware.", "EFI firmware."], "type": "string"}, "hyperthreading": {"description": "CPU hyper-threading support.", "enum": ["CPU_HYPER_THREADING_UNSPECIFIED", "DISABLED", "ENABLED"], "enumDescriptions": ["Unspecified or unknown.", "Hyper-threading is disabled.", "Hyper-threading is enabled."], "type": "string"}, "vendor": {"description": "Hardware vendor.", "type": "string"}}, "type": "object"}, "MachineDetails": {"description": "Details of a machine.", "id": "MachineDetails", "properties": {"architecture": {"$ref": "MachineArchitectureDetails", "description": "Architecture details (vendor, CPU architecture)."}, "coreCount": {"description": "Number of logical CPU cores in the machine. Must be non-negative.", "format": "int32", "type": "integer"}, "createTime": {"description": "Machine creation time.", "format": "google-datetime", "type": "string"}, "diskPartitions": {"$ref": "DiskPartitionDetails", "description": "Optional. Disk partitions details. Note: Partitions are not necessarily mounted on local disks and therefore might not have a one-to-one correspondence with local disks."}, "disks": {"$ref": "MachineDiskDetails", "description": "Disk details."}, "guestOs": {"$ref": "GuestOsDetails", "description": "Guest OS information."}, "machineName": {"description": "Machine name.", "type": "string"}, "memoryMb": {"description": "The amount of memory in the machine. Must be non-negative.", "format": "int32", "type": "integer"}, "network": {"$ref": "MachineNetworkDetails", "description": "Network details."}, "platform": {"$ref": "PlatformDetails", "description": "Platform specific information."}, "powerState": {"description": "Power state of the machine.", "enum": ["POWER_STATE_UNSPECIFIED", "PENDING", "ACTIVE", "SUSPENDING", "SUSPENDED", "DELETING", "DELETED"], "enumDescriptions": ["Power state is unknown.", "The machine is preparing to enter the ACTIVE state. An instance may enter the PENDING state when it launches for the first time, or when it is started after being in the SUSPENDED state.", "The machine is active.", "The machine is being turned off.", "The machine is off.", "The machine is being deleted from the hosting platform.", "The machine is deleted from the hosting platform."], "type": "string"}, "uuid": {"description": "Machine unique identifier.", "type": "string"}}, "type": "object"}, "MachineDiskDetails": {"description": "Details of machine disks.", "id": "MachineDiskDetails", "properties": {"disks": {"$ref": "DiskEntryList", "description": "List of disks."}, "totalCapacityBytes": {"description": "Disk total Capacity.", "format": "int64", "type": "string"}, "totalFreeBytes": {"description": "Total disk free space.", "format": "int64", "type": "string"}}, "type": "object"}, "MachineNetworkDetails": {"description": "Details of network adapters and settings.", "id": "MachineNetworkDetails", "properties": {"adapters": {"$ref": "NetworkAdapterList", "description": "List of network adapters."}, "primaryIpAddress": {"description": "The primary IP address of the machine.", "type": "string"}, "primaryMacAddress": {"description": "MAC address of the machine. This property is used to uniqly identify the machine.", "type": "string"}, "publicIpAddress": {"description": "The public IP address of the machine.", "type": "string"}}, "type": "object"}, "MachinePreferences": {"description": "The type of machines to consider when calculating virtual machine migration insights and recommendations. Not all machine types are available in all zones and regions.", "id": "MachinePreferences", "properties": {"allowedMachineSeries": {"description": "Compute Engine machine series to consider for insights and recommendations. If empty, no restriction is applied on the machine series.", "items": {"$ref": "MachineSeries"}, "type": "array"}}, "type": "object"}, "MachineSeries": {"description": "A machine series, for a target product (e.g. Compute Engine, Google Cloud VMware Engine).", "id": "MachineSeries", "properties": {"code": {"description": "Code to identify a machine series. Consult this for more details on the available series for Compute Engine: https://cloud.google.com/compute/docs/machine-resource#machine_type_comparison Consult this for more details on the available series for Google Cloud VMware Engine: https://cloud.google.com/vmware-engine/pricing", "type": "string"}}, "type": "object"}, "MemoryUsageSample": {"description": "Memory usage sample.", "id": "MemoryUsageSample", "properties": {"utilizedPercentage": {"description": "Percentage of system memory utilized. Must be in the interval [0, 100].", "format": "float", "type": "number"}}, "type": "object"}, "MigrationInsight": {"description": "An insight about potential migrations for an asset.", "id": "MigrationInsight", "properties": {"computeEngineTarget": {"$ref": "ComputeEngineMigrationTarget", "description": "Output only. A Google Compute Engine target.", "readOnly": true}, "fit": {"$ref": "FitDescriptor", "description": "Output only. Description of how well the asset this insight is associated with fits the proposed migration.", "readOnly": true}}, "type": "object"}, "Money": {"description": "Represents an amount of money with its currency type.", "id": "Money", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "MySqlPlugin": {"description": "MySql plugin.", "id": "MySqlPlugin", "properties": {"enabled": {"description": "Required. The plugin is active.", "type": "boolean"}, "plugin": {"description": "Required. The plugin name.", "type": "string"}, "version": {"description": "Required. The plugin version.", "type": "string"}}, "type": "object"}, "MySqlProperty": {"description": "MySql property.", "id": "MySqlProperty", "properties": {"enabled": {"description": "Required. The property is enabled.", "type": "boolean"}, "numericValue": {"description": "Required. The property numeric value.", "format": "int64", "type": "string"}, "property": {"description": "Required. The property name.", "type": "string"}}, "type": "object"}, "MySqlSchemaDetails": {"description": "Specific details for a Mysql database.", "id": "MySqlSchemaDetails", "properties": {"storageEngines": {"description": "Optional. Mysql storage engine tables.", "items": {"$ref": "MySqlStorageEngineDetails"}, "type": "array"}}, "type": "object"}, "MySqlStorageEngineDetails": {"description": "Mysql storage engine tables.", "id": "MySqlStorageEngineDetails", "properties": {"encryptedTableCount": {"description": "Optional. The number of encrypted tables.", "format": "int32", "type": "integer"}, "engine": {"description": "Required. The storage engine.", "enum": ["ENGINE_UNSPECIFIED", "INNODB", "MYISAM", "MEMORY", "CSV", "ARCHIVE", "BLACKHOLE", "NDB", "MERGE", "FEDERATED", "EXAMPLE", "OTHER"], "enumDescriptions": ["Unspecified storage engine.", "InnoDB.", "MyISAM.", "Memory.", "CSV.", "Archive.", "Blackhole.", "NDB.", "Merge.", "Federated.", "Example.", "Other."], "type": "string"}, "tableCount": {"description": "Optional. The number of tables.", "format": "int32", "type": "integer"}}, "type": "object"}, "MySqlVariable": {"description": "MySql variable.", "id": "MySqlVariable", "properties": {"category": {"description": "Required. The variable category.", "type": "string"}, "value": {"description": "Required. The variable value.", "type": "string"}, "variable": {"description": "Required. The variable name.", "type": "string"}}, "type": "object"}, "MysqlDatabaseDeployment": {"description": "Specific details for a Mysql database deployment.", "id": "MysqlDatabaseDeployment", "properties": {"plugins": {"description": "Optional. List of MySql plugins.", "items": {"$ref": "MySqlPlugin"}, "type": "array"}, "properties": {"description": "Optional. List of MySql properties.", "items": {"$ref": "MySqlProperty"}, "type": "array"}, "resourceGroupsCount": {"description": "Optional. Number of resource groups.", "format": "int32", "type": "integer"}, "variables": {"description": "Optional. List of MySql variables.", "items": {"$ref": "MySqlVariable"}, "type": "array"}}, "type": "object"}, "NetworkAdapterDetails": {"description": "Details of network adapter.", "id": "NetworkAdapterDetails", "properties": {"adapterType": {"description": "Network adapter type (e.g. VMXNET3).", "type": "string"}, "addresses": {"$ref": "NetworkAddressList", "description": "NetworkAddressList"}, "macAddress": {"description": "MAC address.", "type": "string"}}, "type": "object"}, "NetworkAdapterList": {"description": "List of network adapters.", "id": "NetworkAdapterList", "properties": {"entries": {"description": "Network adapter entries.", "items": {"$ref": "NetworkAdapterDetails"}, "type": "array"}}, "type": "object"}, "NetworkAddress": {"description": "Details of network address.", "id": "NetworkAddress", "properties": {"assignment": {"description": "Whether DHCP is used to assign addresses.", "enum": ["ADDRESS_ASSIGNMENT_UNSPECIFIED", "ADDRESS_ASSIGNMENT_STATIC", "ADDRESS_ASSIGNMENT_DHCP"], "enumDescriptions": ["Unknown (default value).", "Statically assigned IP.", "Dynamically assigned IP (DHCP)."], "type": "string"}, "bcast": {"description": "Broadcast address.", "type": "string"}, "fqdn": {"description": "Fully qualified domain name.", "type": "string"}, "ipAddress": {"description": "Assigned or configured IP Address.", "type": "string"}, "subnetMask": {"description": "Subnet mask.", "type": "string"}}, "type": "object"}, "NetworkAddressList": {"description": "List of allocated/assigned network addresses.", "id": "NetworkAddressList", "properties": {"entries": {"description": "Network address entries.", "items": {"$ref": "NetworkAddress"}, "type": "array"}}, "type": "object"}, "NetworkConnection": {"id": "NetworkConnection", "properties": {"localIpAddress": {"description": "Local IP address.", "type": "string"}, "localPort": {"description": "Local port.", "format": "int32", "type": "integer"}, "pid": {"description": "Process ID.", "format": "int64", "type": "string"}, "processName": {"description": "Process or service name.", "type": "string"}, "protocol": {"description": "Connection protocol (e.g. TCP/UDP).", "type": "string"}, "remoteIpAddress": {"description": "Remote IP address.", "type": "string"}, "remotePort": {"description": "Remote port.", "format": "int32", "type": "integer"}, "state": {"description": "Network connection state.", "enum": ["STATE_UNSPECIFIED", "OPENING", "OPEN", "LISTEN", "CLOSING", "CLOSED"], "enumDescriptions": ["Connection state is unknown or unspecified.", "The connection is being opened.", "The connection is open.", "Listening for incoming connections.", "The connection is being closed.", "The connection is closed."], "type": "string"}}, "type": "object"}, "NetworkConnectionList": {"description": "Network connection list.", "id": "NetworkConnectionList", "properties": {"entries": {"description": "Network connection entries.", "items": {"$ref": "NetworkConnection"}, "type": "array"}}, "type": "object"}, "NetworkUsageSample": {"description": "Network usage sample. Values are across all network interfaces.", "id": "NetworkUsageSample", "properties": {"averageEgressBps": {"description": "Average network egress in B/s sampled over a short window. Must be non-negative.", "format": "float", "type": "number"}, "averageIngressBps": {"description": "Average network ingress in B/s sampled over a short window. Must be non-negative.", "format": "float", "type": "number"}}, "type": "object"}, "NfsExport": {"description": "NFS export.", "id": "NfsExport", "properties": {"exportDirectory": {"description": "The directory being exported.", "type": "string"}, "hosts": {"description": "The hosts or networks to which the export is being shared.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "NfsExportList": {"description": "NFS exports.", "id": "NfsExportList", "properties": {"entries": {"description": "NFS export entries.", "items": {"$ref": "NfsExport"}, "type": "array"}}, "type": "object"}, "OpenFileDetails": {"description": "Open file Information.", "id": "OpenFileDetails", "properties": {"command": {"description": "Opened file command.", "type": "string"}, "filePath": {"description": "Opened file file path.", "type": "string"}, "fileType": {"description": "Opened file file type.", "type": "string"}, "user": {"description": "Opened file user.", "type": "string"}}, "type": "object"}, "OpenFileList": {"description": "Open file list.", "id": "OpenFileList", "properties": {"entries": {"description": "Open file details entries.", "items": {"$ref": "OpenFileDetails"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "PerformanceSample": {"description": "Performance data sample.", "id": "PerformanceSample", "properties": {"cpu": {"$ref": "CpuUsageSample", "description": "CPU usage sample."}, "disk": {"$ref": "DiskUsageSample", "description": "Disk usage sample."}, "memory": {"$ref": "MemoryUsageSample", "description": "Memory usage sample."}, "network": {"$ref": "NetworkUsageSample", "description": "Network usage sample."}, "sampleTime": {"description": "Time the sample was collected. If omitted, the frame report time will be used.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PhysicalPlatformDetails": {"description": "Platform specific details for Physical Machines.", "id": "PhysicalPlatformDetails", "properties": {"hyperthreading": {"description": "Whether the machine is hyperthreaded.", "enum": ["HYPERTHREADING_STATUS_UNSPECIFIED", "HYPERTHREADING_STATUS_DISABLED", "HYPERTHREADING_STATUS_ENABLED"], "enumDescriptions": ["Simultaneous Multithreading status unknown.", "Simultaneous Multithreading is disabled or unavailable.", "Simultaneous Multithreading is enabled."], "type": "string"}, "location": {"description": "Free text representation of the machine location. The format of this field should not be relied on. Different machines in the same location may have different string values for this field.", "type": "string"}}, "type": "object"}, "PlatformDetails": {"description": "Information about the platform.", "id": "PlatformDetails", "properties": {"awsEc2Details": {"$ref": "AwsEc2PlatformDetails", "description": "AWS EC2 specific details."}, "azureVmDetails": {"$ref": "AzureVmPlatformDetails", "description": "Azure VM specific details."}, "genericDetails": {"$ref": "GenericPlatformDetails", "description": "Generic platform details."}, "physicalDetails": {"$ref": "PhysicalPlatformDetails", "description": "Physical machines platform details."}, "vmwareDetails": {"$ref": "VmwarePlatformDetails", "description": "VMware specific details."}}, "type": "object"}, "PostgreSqlDatabaseDeployment": {"description": "Specific details for a PostgreSQL database deployment.", "id": "PostgreSqlDatabaseDeployment", "properties": {"properties": {"description": "Optional. List of PostgreSql properties.", "items": {"$ref": "PostgreSqlProperty"}, "type": "array"}, "settings": {"description": "Optional. List of PostgreSql settings.", "items": {"$ref": "PostgreSqlSetting"}, "type": "array"}}, "type": "object"}, "PostgreSqlExtension": {"description": "PostgreSql extension.", "id": "PostgreSqlExtension", "properties": {"extension": {"description": "Required. The extension name.", "type": "string"}, "version": {"description": "Required. The extension version.", "type": "string"}}, "type": "object"}, "PostgreSqlProperty": {"description": "PostgreSql property.", "id": "PostgreSqlProperty", "properties": {"enabled": {"description": "Required. The property is enabled.", "type": "boolean"}, "numericValue": {"description": "Required. The property numeric value.", "format": "int64", "type": "string"}, "property": {"description": "Required. The property name.", "type": "string"}}, "type": "object"}, "PostgreSqlSchemaDetails": {"description": "Specific details for a PostgreSql schema.", "id": "PostgreSqlSchemaDetails", "properties": {"foreignTablesCount": {"description": "Optional. PostgreSql foreign tables.", "format": "int32", "type": "integer"}, "postgresqlExtensions": {"description": "Optional. PostgreSql extensions.", "items": {"$ref": "PostgreSqlExtension"}, "type": "array"}}, "type": "object"}, "PostgreSqlSetting": {"description": "PostgreSql setting.", "id": "PostgreSqlSetting", "properties": {"boolValue": {"description": "Required. The setting boolean value.", "type": "boolean"}, "intValue": {"description": "Required. The setting int value.", "format": "int64", "type": "string"}, "realValue": {"description": "Required. The setting real value.", "format": "float", "type": "number"}, "setting": {"description": "Required. The setting name.", "type": "string"}, "source": {"description": "Required. The setting source.", "type": "string"}, "stringValue": {"description": "Required. The setting string value. Notice that enum values are stored as strings.", "type": "string"}, "unit": {"description": "Optional. The setting unit.", "type": "string"}}, "type": "object"}, "PreferenceSet": {"description": "The preferences that apply to all assets in a given context.", "id": "PreferenceSet", "properties": {"createTime": {"description": "Output only. The timestamp when the preference set was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of the preference set.", "type": "string"}, "displayName": {"description": "User-friendly display name. Maximum length is 63 characters.", "type": "string"}, "name": {"description": "Output only. Name of the preference set.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the preference set was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "virtualMachinePreferences": {"$ref": "VirtualMachinePreferences", "description": "Optional. A set of preferences that applies to all virtual machines in the context."}}, "type": "object"}, "RegionPreferences": {"description": "The user preferences relating to target regions.", "id": "RegionPreferences", "properties": {"preferredRegions": {"description": "A list of preferred regions, ordered by the most preferred region first. Set only valid Google Cloud region names. See https://cloud.google.com/compute/docs/regions-zones for available regions.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Relation": {"description": "Message representing a relation between 2 resource.", "id": "Relation", "properties": {"createTime": {"description": "Output only. The timestamp when the relation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dstAsset": {"description": "Output only. The destination asset name in the relation.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. The identifier of the relation.", "readOnly": true, "type": "string"}, "srcAsset": {"description": "Output only. The source asset name in the relation.", "readOnly": true, "type": "string"}, "type": {"description": "Optional. The type of the relation.", "enum": ["TYPE_UNSPECIFIED", "LOGICAL_DATABASE", "DATABASE_DEPLOYMENT_HOSTING_SERVER"], "enumDescriptions": ["Default value.", "DBDeployment -> Database", "A relation between a machine/VM and the database deployment it hosts."], "type": "string"}}, "type": "object"}, "RemoveAssetsFromGroupRequest": {"description": "A request to remove assets from a group.", "id": "RemoveAssetsFromGroupRequest", "properties": {"allowMissing": {"description": "Optional. When this value is set to `false` and one of the given assets is not an existing member of the group, the operation fails with a `Not Found` error. When set to `true` this situation is silently ignored by the server. Default value is `false`.", "type": "boolean"}, "assets": {"$ref": "AssetList", "description": "Required. List of assets to be removed. The maximum number of assets that can be removed in a single request is 1000."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "Report": {"description": "Report represents a point-in-time rendering of the ReportConfig results.", "id": "Report", "properties": {"createTime": {"description": "Output only. Creation timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Free-text description.", "type": "string"}, "displayName": {"description": "User-friendly display name. Maximum length is 63 characters.", "type": "string"}, "name": {"description": "Output only. Name of resource.", "readOnly": true, "type": "string"}, "state": {"description": "Report creation state.", "enum": ["STATE_UNSPECIFIED", "PENDING", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Default Report creation state.", "Creating Report.", "Successfully created Report.", "Failed to create Report."], "type": "string"}, "summary": {"$ref": "ReportSummary", "description": "Output only. Summary view of the Report.", "readOnly": true}, "type": {"description": "Report type.", "enum": ["TYPE_UNSPECIFIED", "TOTAL_COST_OF_OWNERSHIP"], "enumDescriptions": ["Default Report type.", "Total cost of ownership Report type."], "type": "string"}, "updateTime": {"description": "Output only. Last update timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ReportAssetFramesResponse": {"description": "A response to a call to `ReportAssetFrame`.", "id": "ReportAssetFramesResponse", "properties": {}, "type": "object"}, "ReportConfig": {"description": "The groups and associated preference sets on which we can generate reports.", "id": "ReportConfig", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Free-text description.", "type": "string"}, "displayName": {"description": "User-friendly display name. Maximum length is 63 characters.", "type": "string"}, "groupPreferencesetAssignments": {"description": "Required. Collection of combinations of groups and preference sets.", "items": {"$ref": "ReportConfigGroupPreferenceSetAssignment"}, "type": "array"}, "name": {"description": "Output only. Name of resource.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ReportConfigGroupPreferenceSetAssignment": {"description": "Represents a combination of a group with a preference set.", "id": "ReportConfigGroupPreferenceSetAssignment", "properties": {"group": {"description": "Required. Name of the group.", "type": "string"}, "preferenceSet": {"description": "Required. Name of the Preference Set.", "type": "string"}}, "type": "object"}, "ReportSummary": {"description": "Describes the Summary view of a Report, which contains aggregated values for all the groups and preference sets included in this Report.", "id": "ReportSummary", "properties": {"allAssetsStats": {"$ref": "ReportSummaryAssetAggregateStats", "description": "Aggregate statistics for all the assets across all the groups."}, "groupFindings": {"description": "Findings for each Group included in this report.", "items": {"$ref": "ReportSummaryGroupFinding"}, "type": "array"}}, "type": "object"}, "ReportSummaryAssetAggregateStats": {"description": "Aggregate statistics for a collection of assets.", "id": "ReportSummaryAssetAggregateStats", "properties": {"coreCountHistogram": {"$ref": "ReportSummaryHistogramChartData", "description": "Histogram showing a distribution of logical CPU core counts."}, "memoryBytesHistogram": {"$ref": "ReportSummaryHistogramChartData", "description": "Histogram showing a distribution of memory sizes."}, "memoryUtilizationChart": {"$ref": "ReportSummaryUtilizationChartData", "description": "Total memory split into Used/Free buckets."}, "operatingSystem": {"$ref": "ReportSummaryChartData", "description": "Count of assets grouped by Operating System families."}, "storageBytesHistogram": {"$ref": "ReportSummaryHistogramChartData", "description": "Histogram showing a distribution of storage sizes."}, "storageUtilizationChart": {"$ref": "ReportSummaryUtilizationChartData", "description": "Total memory split into Used/Free buckets."}, "totalAssets": {"description": "Count of the number of unique assets in this collection.", "format": "int64", "type": "string"}, "totalCores": {"description": "Sum of the CPU core count of all the assets in this collection.", "format": "int64", "type": "string"}, "totalMemoryBytes": {"description": "Sum of the memory in bytes of all the assets in this collection.", "format": "int64", "type": "string"}, "totalStorageBytes": {"description": "Sum of persistent storage in bytes of all the assets in this collection.", "format": "int64", "type": "string"}}, "type": "object"}, "ReportSummaryChartData": {"description": "Describes a collection of data points rendered as a Chart.", "id": "ReportSummaryChartData", "properties": {"dataPoints": {"description": "Each data point in the chart is represented as a name-value pair with the name being the x-axis label, and the value being the y-axis value.", "items": {"$ref": "ReportSummaryChartDataDataPoint"}, "type": "array"}}, "type": "object"}, "ReportSummaryChartDataDataPoint": {"description": "Describes a single data point in the Chart.", "id": "ReportSummaryChartDataDataPoint", "properties": {"label": {"description": "The X-axis label for this data point.", "type": "string"}, "value": {"description": "The Y-axis value for this data point.", "format": "double", "type": "number"}}, "type": "object"}, "ReportSummaryComputeEngineFinding": {"description": "A set of findings that applies to assets destined for Compute Engine.", "id": "ReportSummaryComputeEngineFinding", "properties": {"allocatedAssetCount": {"description": "Count of assets which were allocated.", "format": "int64", "type": "string"}, "allocatedDiskTypes": {"description": "Set of disk types allocated to assets.", "items": {"enum": ["PERSISTENT_DISK_TYPE_UNSPECIFIED", "PERSISTENT_DISK_TYPE_STANDARD", "PERSISTENT_DISK_TYPE_BALANCED", "PERSISTENT_DISK_TYPE_SSD"], "enumDescriptions": ["Unspecified. Fallback to default value based on context.", "Standard HDD Persistent Disk.", "Balanced Persistent <PERSON>.", "SSD Persistent Disk."], "type": "string"}, "type": "array"}, "allocatedRegions": {"description": "Set of regions in which the assets were allocated.", "items": {"type": "string"}, "type": "array"}, "machineSeriesAllocations": {"description": "Distribution of assets based on the Machine Series.", "items": {"$ref": "ReportSummaryMachineSeriesAllocation"}, "type": "array"}}, "type": "object"}, "ReportSummaryGroupFinding": {"description": "Summary Findings for a specific Group.", "id": "ReportSummaryGroupFinding", "properties": {"assetAggregateStats": {"$ref": "ReportSummaryAssetAggregateStats", "description": "Summary statistics for all the assets in this group."}, "description": {"description": "Description for the Group.", "type": "string"}, "displayName": {"description": "Display Name for the Group.", "type": "string"}, "overlappingAssetCount": {"deprecated": true, "description": "This field is deprecated, do not rely on it having a value.", "format": "int64", "type": "string"}, "preferenceSetFindings": {"description": "Findings for each of the PreferenceSets for this group.", "items": {"$ref": "ReportSummaryGroupPreferenceSetFinding"}, "type": "array"}}, "type": "object"}, "ReportSummaryGroupPreferenceSetFinding": {"description": "Summary Findings for a specific Group/PreferenceSet combination.", "id": "ReportSummaryGroupPreferenceSetFinding", "properties": {"computeEngineFinding": {"$ref": "ReportSummaryComputeEngineFinding", "description": "A set of findings that applies to Compute Engine machines in the input."}, "description": {"description": "Description for the Preference Set.", "type": "string"}, "displayName": {"description": "Display Name of the Preference Set", "type": "string"}, "machinePreferences": {"$ref": "VirtualMachinePreferences", "description": "A set of preferences that applies to all machines in the context."}, "monthlyCostCompute": {"$ref": "Money", "description": "Compute monthly cost for this preference set."}, "monthlyCostNetworkEgress": {"$ref": "Money", "description": "Network Egress monthly cost for this preference set."}, "monthlyCostOsLicense": {"$ref": "Money", "description": "Licensing monthly cost for this preference set."}, "monthlyCostOther": {"$ref": "Money", "description": "Miscellaneous monthly cost for this preference set."}, "monthlyCostStorage": {"$ref": "Money", "description": "Storage monthly cost for this preference set."}, "monthlyCostTotal": {"$ref": "Money", "description": "Total monthly cost for this preference set."}, "soleTenantFinding": {"$ref": "ReportSummarySoleTenantFinding", "description": "A set of findings that applies to Sole-Tenant machines in the input."}, "vmwareEngineFinding": {"$ref": "ReportSummaryVmwareEngineFinding", "description": "A set of findings that applies to VMWare machines in the input."}}, "type": "object"}, "ReportSummaryHistogramChartData": {"description": "A Histogram Chart shows a distribution of values into buckets, showing a count of values which fall into a bucket.", "id": "ReportSummaryHistogramChartData", "properties": {"buckets": {"description": "Buckets in the histogram. There will be `n+1` buckets matching `n` lower bounds in the request. The first bucket will be from -infinity to the first bound. Subsequent buckets will be between one bound and the next. The final bucket will be from the final bound to infinity.", "items": {"$ref": "ReportSummaryHistogramChartDataBucket"}, "type": "array"}}, "type": "object"}, "ReportSummaryHistogramChartDataBucket": {"description": "A histogram bucket with a lower and upper bound, and a count of items with a field value between those bounds. The lower bound is inclusive and the upper bound is exclusive. Lower bound may be -infinity and upper bound may be infinity.", "id": "ReportSummaryHistogramChartDataBucket", "properties": {"count": {"description": "Count of items in the bucket.", "format": "int64", "type": "string"}, "lowerBound": {"description": "Lower bound - inclusive.", "format": "int64", "type": "string"}, "upperBound": {"description": "Upper bound - exclusive.", "format": "int64", "type": "string"}}, "type": "object"}, "ReportSummaryMachineSeriesAllocation": {"description": "Represents a data point tracking the count of assets allocated for a specific Machine Series.", "id": "ReportSummaryMachineSeriesAllocation", "properties": {"allocatedAssetCount": {"description": "Count of assets allocated to this machine series.", "format": "int64", "type": "string"}, "machineSeries": {"$ref": "MachineSeries", "description": "The Machine Series (e.g. \"E2\", \"N2\")"}}, "type": "object"}, "ReportSummarySoleTenantFinding": {"description": "A set of findings that applies to assets destined for Sole-Tenant nodes.", "id": "ReportSummarySoleTenantFinding", "properties": {"allocatedAssetCount": {"description": "Count of assets which are allocated", "format": "int64", "type": "string"}, "allocatedRegions": {"description": "Set of regions in which the assets are allocated", "items": {"type": "string"}, "type": "array"}, "nodeAllocations": {"description": "Set of per-nodetype allocation records", "items": {"$ref": "ReportSummarySoleTenantNodeAllocation"}, "type": "array"}}, "type": "object"}, "ReportSummarySoleTenantNodeAllocation": {"description": "Represents the assets allocated to a specific Sole-Tenant node type.", "id": "ReportSummarySoleTenantNodeAllocation", "properties": {"allocatedAssetCount": {"description": "Count of assets allocated to these nodes", "format": "int64", "type": "string"}, "node": {"$ref": "SoleTenantNodeType", "description": "Sole Tenant node type, e.g. \"m3-node-128-3904\""}, "nodeCount": {"description": "Count of this node type to be provisioned", "format": "int64", "type": "string"}}, "type": "object"}, "ReportSummaryUtilizationChartData": {"description": "Utilization Chart is a specific type of visualization which displays a metric classified into \"Used\" and \"Free\" buckets.", "id": "ReportSummaryUtilizationChartData", "properties": {"free": {"description": "Aggregate value which falls into the \"Free\" bucket.", "format": "int64", "type": "string"}, "used": {"description": "Aggregate value which falls into the \"Used\" bucket.", "format": "int64", "type": "string"}}, "type": "object"}, "ReportSummaryVmwareEngineFinding": {"description": "A set of findings that applies to assets destined for VMWare Engine.", "id": "ReportSummaryVmwareEngineFinding", "properties": {"allocatedAssetCount": {"description": "Count of assets which are allocated", "format": "int64", "type": "string"}, "allocatedRegions": {"description": "Set of regions in which the assets were allocated", "items": {"type": "string"}, "type": "array"}, "nodeAllocations": {"description": "Set of per-nodetype allocation records", "items": {"$ref": "ReportSummaryVmwareNodeAllocation"}, "type": "array"}}, "type": "object"}, "ReportSummaryVmwareNode": {"description": "A VMWare Engine Node", "id": "ReportSummaryVmwareNode", "properties": {"code": {"description": "Code to identify VMware Engine node series, e.g. \"ve1-standard-72\". Based on the displayName of cloud.google.com/vmware-engine/docs/reference/rest/v1/projects.locations.nodeTypes", "type": "string"}}, "type": "object"}, "ReportSummaryVmwareNodeAllocation": {"description": "Represents assets allocated to a specific VMWare Node type.", "id": "ReportSummaryVmwareNodeAllocation", "properties": {"allocatedAssetCount": {"description": "Count of assets allocated to these nodes", "format": "int64", "type": "string"}, "nodeCount": {"description": "Count of this node type to be provisioned", "format": "int64", "type": "string"}, "vmwareNode": {"$ref": "ReportSummaryVmwareNode", "description": "VMWare node type, e.g. \"ve1-standard-72\""}}, "type": "object"}, "RunImportJobRequest": {"description": "A request to run an import job.", "id": "RunImportJobRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "RunningProcess": {"description": "Guest OS running process details.", "id": "RunningProcess", "properties": {"attributes": {"additionalProperties": {"type": "string"}, "description": "Process extended attributes.", "type": "object"}, "cmdline": {"description": "Process full command line.", "type": "string"}, "exePath": {"description": "Process binary path.", "type": "string"}, "pid": {"description": "Process ID.", "format": "int64", "type": "string"}, "user": {"description": "User running the process.", "type": "string"}}, "type": "object"}, "RunningProcessList": {"description": "List of running guest OS processes.", "id": "RunningProcessList", "properties": {"entries": {"description": "Running process entries.", "items": {"$ref": "RunningProcess"}, "type": "array"}}, "type": "object"}, "RunningService": {"description": "Guest OS running service details.", "id": "RunningService", "properties": {"cmdline": {"description": "Service command line.", "type": "string"}, "exePath": {"description": "Service binary path.", "type": "string"}, "pid": {"description": "Service pid.", "format": "int64", "type": "string"}, "serviceName": {"description": "Service name.", "type": "string"}, "startMode": {"description": "Service start mode (OS-agnostic).", "enum": ["START_MODE_UNSPECIFIED", "BOOT", "SYSTEM", "AUTO", "MANUAL", "DISABLED"], "enumDescriptions": ["Start mode unspecified.", "The service is a device driver started by the system loader.", "The service is a device driver started by the IOInitSystem function.", "The service is started by the operating system, at system start-up", "The service is started only manually, by a user.", "The service is disabled."], "type": "string"}, "state": {"description": "Service state (OS-agnostic).", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "PAUSED", "STOPPED"], "enumDescriptions": ["Service state unspecified.", "Service is active.", "Service is paused.", "Service is stopped."], "type": "string"}}, "type": "object"}, "RunningServiceList": {"description": "List of running guest OS services.", "id": "RunningServiceList", "properties": {"entries": {"description": "Running service entries.", "items": {"$ref": "RunningService"}, "type": "array"}}, "type": "object"}, "RuntimeNetworkInfo": {"description": "Runtime networking information.", "id": "RuntimeNetworkInfo", "properties": {"connections": {"$ref": "NetworkConnectionList", "description": "Network connections."}, "scanTime": {"description": "Time of the last network scan.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "SendDiscoveryClientHeartbeatRequest": {"description": "A request to send a discovery client heartbeat.", "id": "SendDiscoveryClientHeartbeatRequest", "properties": {"errors": {"description": "Optional. Errors affecting client functionality.", "items": {"$ref": "Status"}, "type": "array"}, "version": {"description": "Optional. Client application version.", "type": "string"}}, "type": "object"}, "Settings": {"description": "Describes the Migration Center settings related to the project.", "id": "Settings", "properties": {"disableCloudLogging": {"description": "Disable Cloud Logging for the Migration Center API. Users are billed for the logs.", "type": "boolean"}, "name": {"description": "Output only. The name of the resource.", "readOnly": true, "type": "string"}, "preferenceSet": {"description": "The preference set used by default for a project.", "type": "string"}}, "type": "object"}, "SoleTenancyPreferences": {"description": "Preferences concerning Sole Tenancy nodes and VMs.", "id": "SoleTenancyPreferences", "properties": {"commitmentPlan": {"description": "Commitment plan to consider when calculating costs for virtual machine insights and recommendations. If you are unsure which value to set, a 3 year commitment plan is often a good value to start with.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "ON_DEMAND", "COMMITMENT_1_YEAR", "COMMITMENT_3_YEAR"], "enumDescriptions": ["Unspecified commitment plan.", "No commitment plan (on-demand usage).", "1 year commitment.", "3 years commitment."], "type": "string"}, "cpuOvercommitRatio": {"description": "CPU overcommit ratio. Acceptable values are between 1.0 and 2.0 inclusive.", "format": "double", "type": "number"}, "hostMaintenancePolicy": {"description": "Sole Tenancy nodes maintenance policy.", "enum": ["HOST_MAINTENANCE_POLICY_UNSPECIFIED", "HOST_MAINTENANCE_POLICY_DEFAULT", "HOST_MAINTENANCE_POLICY_RESTART_IN_PLACE", "HOST_MAINTENANCE_POLICY_MIGRATE_WITHIN_NODE_GROUP"], "enumDescriptions": ["Unspecified host maintenance policy.", "Default host maintenance policy.", "Restart in place host maintenance policy.", "Migrate within node group host maintenance policy."], "type": "string"}, "nodeTypes": {"description": "A list of sole tenant node types. An empty list means that all possible node types will be considered.", "items": {"$ref": "SoleTenantNodeType"}, "type": "array"}}, "type": "object"}, "SoleTenantNodeType": {"description": "A Sole Tenant node type.", "id": "SoleTenantNodeType", "properties": {"nodeName": {"description": "Name of the Sole Tenant node. Consult https://cloud.google.com/compute/docs/nodes/sole-tenant-nodes", "type": "string"}}, "type": "object"}, "Source": {"description": "Source represents an object from which asset information is streamed to Migration Center.", "id": "Source", "properties": {"createTime": {"description": "Output only. The timestamp when the source was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Free-text description.", "type": "string"}, "displayName": {"description": "User-friendly display name.", "type": "string"}, "errorFrameCount": {"description": "Output only. The number of frames that were reported by the source and contained errors.", "format": "int32", "readOnly": true, "type": "integer"}, "managed": {"description": "If `true`, the source is managed by other service(s).", "type": "boolean"}, "name": {"description": "Output only. The full name of the source.", "readOnly": true, "type": "string"}, "pendingFrameCount": {"description": "Output only. Number of frames that are still being processed.", "format": "int32", "readOnly": true, "type": "integer"}, "priority": {"description": "The information confidence of the source. The higher the value, the higher the confidence.", "format": "int32", "type": "integer"}, "state": {"description": "Output only. The state of the source.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DELETING", "INVALID"], "enumDescriptions": ["Unspecified.", "The source is active and ready to be used.", "In the process of being deleted.", "Source is in an invalid state. Asset frames reported to it will be ignored."], "readOnly": true, "type": "string"}, "type": {"description": "Data source type.", "enum": ["SOURCE_TYPE_UNKNOWN", "SOURCE_TYPE_UPLOAD", "SOURCE_TYPE_GUEST_OS_SCAN", "SOURCE_TYPE_INVENTORY_SCAN", "SOURCE_TYPE_CUSTOM", "SOURCE_TYPE_DISCOVERY_CLIENT"], "enumDescriptions": ["Unspecified", "Manually uploaded file (e.g. CSV)", "Guest-level info", "Inventory-level scan", "Third-party owned sources.", "Discovery clients"], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the source was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SqlServerDatabaseDeployment": {"description": "Specific details for a Microsoft SQL Server database deployment.", "id": "SqlServerDatabaseDeployment", "properties": {"features": {"description": "Optional. List of SQL Server features.", "items": {"$ref": "SqlServerFeature"}, "type": "array"}, "serverFlags": {"description": "Optional. List of SQL Server server flags.", "items": {"$ref": "SqlServerServerFlag"}, "type": "array"}, "traceFlags": {"description": "Optional. List of SQL Server trace flags.", "items": {"$ref": "SqlServerTraceFlag"}, "type": "array"}}, "type": "object"}, "SqlServerFeature": {"description": "SQL Server feature details.", "id": "SqlServerFeature", "properties": {"enabled": {"description": "Required. Field enabled is set when a feature is used on the source deployment.", "type": "boolean"}, "featureName": {"description": "Required. The feature name.", "type": "string"}}, "type": "object"}, "SqlServerSchemaDetails": {"description": "Specific details for a SqlServer database.", "id": "SqlServerSchemaDetails", "properties": {"clrObjectCount": {"description": "Optional. SqlServer number of CLR objects.", "format": "int32", "type": "integer"}}, "type": "object"}, "SqlServerServerFlag": {"description": "SQL Server server flag details.", "id": "SqlServerServerFlag", "properties": {"serverFlagName": {"description": "Required. The server flag name.", "type": "string"}, "value": {"description": "Required. The server flag value set by the user.", "type": "string"}, "valueInUse": {"description": "Required. The server flag actual value. If `value_in_use` is different from `value` it means that either the configuration change was not applied or it is an expected behavior. See SQL Server documentation for more details.", "type": "string"}}, "type": "object"}, "SqlServerTraceFlag": {"description": "SQL Server trace flag details.", "id": "SqlServerTraceFlag", "properties": {"scope": {"description": "Required. The trace flag scope.", "enum": ["SCOPE_UNSPECIFIED", "OFF", "GLOBAL", "SESSION"], "enumDescriptions": ["Unspecified.", "Off.", "Global.", "Session."], "type": "string"}, "traceFlagName": {"description": "Required. The trace flag name.", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "UpdateAssetRequest": {"description": "A request to update an asset.", "id": "UpdateAssetRequest", "properties": {"asset": {"$ref": "<PERSON><PERSON>", "description": "Required. The resource being updated."}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the `Asset` resource by the update. The values specified in the `update_mask` field are relative to the resource, not the full request. A field will be overwritten if it is in the mask. A single * value in the mask lets you to overwrite all fields.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "UploadFileInfo": {"description": "A resource that contains a URI to which a data file can be uploaded.", "id": "UploadFileInfo", "properties": {"headers": {"additionalProperties": {"type": "string"}, "description": "Output only. The headers that were used to sign the URI.", "readOnly": true, "type": "object"}, "signedUri": {"description": "Output only. Upload URI for the file.", "readOnly": true, "type": "string"}, "uriExpirationTime": {"description": "Output only. Expiration time of the upload URI.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ValidateImportJobRequest": {"description": "A request to validate an import job.", "id": "ValidateImportJobRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}}, "type": "object"}, "ValidationReport": {"description": "A resource that aggregates errors across import job files.", "id": "ValidationReport", "properties": {"fileValidations": {"description": "List of errors found in files.", "items": {"$ref": "FileValidationReport"}, "type": "array"}, "jobErrors": {"description": "List of job level errors.", "items": {"$ref": "ImportError"}, "type": "array"}}, "type": "object"}, "VirtualMachinePreferences": {"description": "VirtualMachinePreferences enables you to create sets of assumptions, for example, a geographical location and pricing track, for your migrated virtual machines. The set of preferences influence recommendations for migrating virtual machine assets.", "id": "VirtualMachinePreferences", "properties": {"commitmentPlan": {"description": "Commitment plan to consider when calculating costs for virtual machine insights and recommendations. If you are unsure which value to set, a 3 year commitment plan is often a good value to start with.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "COMMITMENT_PLAN_NONE", "COMMITMENT_PLAN_ONE_YEAR", "COMMITMENT_PLAN_THREE_YEARS"], "enumDescriptions": ["Unspecified commitment plan.", "No commitment plan.", "1 year commitment.", "3 years commitment."], "type": "string"}, "computeEnginePreferences": {"$ref": "ComputeEnginePreferences", "description": "Compute Engine preferences concern insights and recommendations for Compute Engine target."}, "regionPreferences": {"$ref": "RegionPreferences", "description": "Region preferences for assets using this preference set. If you are unsure which value to set, the migration service API region is often a good value to start with."}, "sizingOptimizationStrategy": {"description": "Sizing optimization strategy specifies the preferred strategy used when extrapolating usage data to calculate insights and recommendations for a virtual machine. If you are unsure which value to set, a moderate sizing optimization strategy is often a good value to start with.", "enum": ["SIZING_OPTIMIZATION_STRATEGY_UNSPECIFIED", "SIZING_OPTIMIZATION_STRATEGY_SAME_AS_SOURCE", "SIZING_OPTIMIZATION_STRATEGY_MODERATE", "SIZING_OPTIMIZATION_STRATEGY_AGGRESSIVE"], "enumDescriptions": ["Unspecified (default value).", "No optimization applied. Virtual machine sizing matches as closely as possible the machine shape on the source site, not considering any actual performance data.", "Virtual machine sizing will match the reported usage and shape, with some slack. This a good value to start with.", "Virtual machine sizing will match the reported usage, with little slack. Using this option can help reduce costs."], "type": "string"}, "soleTenancyPreferences": {"$ref": "SoleTenancyPreferences", "description": "Preferences concerning Sole Tenant nodes and virtual machines."}, "targetProduct": {"description": "Target product for assets using this preference set. Specify either target product or business goal, but not both.", "enum": ["COMPUTE_MIGRATION_TARGET_PRODUCT_UNSPECIFIED", "COMPUTE_MIGRATION_TARGET_PRODUCT_COMPUTE_ENGINE", "COMPUTE_MIGRATION_TARGET_PRODUCT_VMWARE_ENGINE", "COMPUTE_MIGRATION_TARGET_PRODUCT_SOLE_TENANCY"], "enumDescriptions": ["Unspecified (default value).", "Prefer to migrate to Google Cloud Compute Engine.", "Prefer to migrate to Google Cloud VMware Engine.6278", "Prefer to migrate to Google Cloud Sole Tenant Nodes."], "type": "string"}, "vmwareEnginePreferences": {"$ref": "VmwareEnginePreferences", "description": "Preferences concerning insights and recommendations for Google Cloud VMware Engine."}}, "type": "object"}, "VmwareDiskConfig": {"description": "VMware disk config details.", "id": "VmwareDiskConfig", "properties": {"backingType": {"description": "VMDK backing type.", "enum": ["BACKING_TYPE_UNSPECIFIED", "BACKING_TYPE_FLAT_V1", "BACKING_TYPE_FLAT_V2", "BACKING_TYPE_PMEM", "BACKING_TYPE_RDM_V1", "BACKING_TYPE_RDM_V2", "BACKING_TYPE_SESPARSE", "BACKING_TYPE_SESPARSE_V1", "BACKING_TYPE_SESPARSE_V2"], "enumDescriptions": ["Default value.", "Flat v1.", "Flat v2.", "Persistent memory, also known as Non-Volatile Memory (NVM).", "Raw Disk Memory v1.", "Raw Disk Memory v2.", "SEsparse is a snapshot format introduced in vSphere 5.5 for large disks.", "SEsparse v1.", "SEsparse v1."], "type": "string"}, "rdmCompatibility": {"description": "RDM compatibility mode.", "enum": ["RDM_COMPATIBILITY_UNSPECIFIED", "PHYSICAL_COMPATIBILITY", "VIRTUAL_COMPATIBILITY"], "enumDescriptions": ["Compatibility mode unspecified or unknown.", "Physical compatibility mode.", "Virtual compatibility mode."], "type": "string"}, "shared": {"description": "Is VMDK shared with other VMs.", "type": "boolean"}, "vmdkMode": {"description": "VMDK disk mode.", "enum": ["VMDK_MODE_UNSPECIFIED", "DEPENDENT", "INDEPENDENT_PERSISTENT", "INDEPENDENT_NONPERSISTENT"], "enumDescriptions": ["VMDK disk mode unspecified or unknown.", "Dependent disk mode.", "Independent - Persistent disk mode.", "Independent - Nonpersistent disk mode."], "type": "string"}}, "type": "object"}, "VmwareEnginePreferences": {"description": "The user preferences relating to Google Cloud VMware Engine target platform.", "id": "VmwareEnginePreferences", "properties": {"commitmentPlan": {"description": "Commitment plan to consider when calculating costs for virtual machine insights and recommendations. If you are unsure which value to set, a 3 year commitment plan is often a good value to start with.", "enum": ["COMMITMENT_PLAN_UNSPECIFIED", "ON_DEMAND", "COMMITMENT_1_YEAR_MONTHLY_PAYMENTS", "COMMITMENT_3_YEAR_MONTHLY_PAYMENTS", "COMMITMENT_1_YEAR_UPFRONT_PAYMENT", "COMMITMENT_3_YEAR_UPFRONT_PAYMENT"], "enumDescriptions": ["Unspecified commitment plan.", "No commitment plan (on-demand usage).", "1 year commitment (monthly payments).", "3 year commitment (monthly payments).", "1 year commitment (upfront payment).", "3 years commitment (upfront payment)."], "type": "string"}, "cpuOvercommitRatio": {"description": "CPU overcommit ratio. Acceptable values are between 1.0 and 8.0, with 0.1 increment.", "format": "double", "type": "number"}, "memoryOvercommitRatio": {"description": "Memory overcommit ratio. Acceptable values are 1.0, 1.25, 1.5, 1.75 and 2.0.", "format": "double", "type": "number"}, "storageDeduplicationCompressionRatio": {"description": "The Deduplication and Compression ratio is based on the logical (Used Before) space required to store data before applying deduplication and compression, in relation to the physical (Used After) space required after applying deduplication and compression. Specifically, the ratio is the Used Before space divided by the Used After space. For example, if the Used Before space is 3 GB, but the physical Used After space is 1 GB, the deduplication and compression ratio is 3x. Acceptable values are between 1.0 and 4.0.", "format": "double", "type": "number"}}, "type": "object"}, "VmwarePlatformDetails": {"description": "VMware specific details.", "id": "VmwarePlatformDetails", "properties": {"esxHyperthreading": {"description": "Whether the ESX is hyperthreaded.", "enum": ["HYPERTHREADING_STATUS_UNSPECIFIED", "HYPERTHREADING_STATUS_DISABLED", "HYPERTHREADING_STATUS_ENABLED"], "enumDescriptions": ["Simultaneous Multithreading status unknown.", "Simultaneous Multithreading is disabled or unavailable.", "Simultaneous Multithreading is enabled."], "type": "string"}, "esxVersion": {"description": "ESX version.", "type": "string"}, "osid": {"description": "VMware os enum - https://vdc-repo.vmware.com/vmwb-repository/dcr-public/da47f910-60ac-438b-8b9b-6122f4d14524/16b7274a-bf8b-4b4c-a05e-746f2aa93c8c/doc/vim.vm.GuestOsDescriptor.GuestOsIdentifier.html.", "type": "string"}, "vcenterFolder": {"description": "Folder name in vCenter where asset resides.", "type": "string"}, "vcenterUri": {"description": "vCenter URI used in collection.", "type": "string"}, "vcenterVersion": {"description": "vCenter version.", "type": "string"}, "vcenterVmId": {"description": "vCenter VM ID.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Migration Center API", "version": "v1", "version_module": true}