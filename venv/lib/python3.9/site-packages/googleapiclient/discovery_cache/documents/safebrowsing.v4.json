{"basePath": "", "baseUrl": "https://safebrowsing.googleapis.com/", "batchPath": "batch", "canonicalName": "Safebrowsing", "description": "Enables client applications to check web resources (most commonly URLs) against Google-generated lists of unsafe web resources. The Safe Browsing APIs are for non-commercial use only. If you need to use APIs to detect malicious URLs for commercial purposes – meaning “for sale or revenue-generating purposes” – please refer to the Web Risk API.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/safe-browsing/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "safebrowsing:v4", "kind": "discovery#restDescription", "mtlsRootUrl": "https://safebrowsing.mtls.googleapis.com/", "name": "safebrowsing", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"encodedFullHashes": {"methods": {"get": {"description": "", "flatPath": "v4/encodedFullHashes/{encodedRequest}", "httpMethod": "GET", "id": "safebrowsing.encodedFullHashes.get", "parameterOrder": ["encodedRequest"], "parameters": {"clientId": {"description": "A client ID that (hopefully) uniquely identifies the client implementation of the Safe Browsing API.", "location": "query", "type": "string"}, "clientVersion": {"description": "The version of the client implementation.", "location": "query", "type": "string"}, "encodedRequest": {"description": "A serialized FindFullHashesRequest proto.", "format": "byte", "location": "path", "required": true, "type": "string"}}, "path": "v4/encodedFullHashes/{encodedRequest}", "response": {"$ref": "GoogleSecuritySafebrowsingV4FindFullHashesResponse"}}}}, "encodedUpdates": {"methods": {"get": {"description": "", "flatPath": "v4/encodedUpdates/{encodedRequest}", "httpMethod": "GET", "id": "safebrowsing.encodedUpdates.get", "parameterOrder": ["encodedRequest"], "parameters": {"clientId": {"description": "A client ID that uniquely identifies the client implementation of the Safe Browsing API.", "location": "query", "type": "string"}, "clientVersion": {"description": "The version of the client implementation.", "location": "query", "type": "string"}, "encodedRequest": {"description": "A serialized FetchThreatListUpdatesRequest proto.", "format": "byte", "location": "path", "required": true, "type": "string"}}, "path": "v4/encodedUpdates/{encodedRequest}", "response": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponse"}}}}, "fullHashes": {"methods": {"find": {"description": "Finds the full hashes that match the requested hash prefixes.", "flatPath": "v4/fullHashes:find", "httpMethod": "POST", "id": "safebrowsing.fullHashes.find", "parameterOrder": [], "parameters": {}, "path": "v4/fullHashes:find", "request": {"$ref": "GoogleSecuritySafebrowsingV4FindFullHashesRequest"}, "response": {"$ref": "GoogleSecuritySafebrowsingV4FindFullHashesResponse"}}}}, "threatHits": {"methods": {"create": {"description": "Reports a Safe Browsing threat list hit to Google. Only projects with TRUSTED_REPORTER visibility can use this method.", "flatPath": "v4/threatHits", "httpMethod": "POST", "id": "safebrowsing.threatHits.create", "parameterOrder": [], "parameters": {}, "path": "v4/threatHits", "request": {"$ref": "GoogleSecuritySafebrowsingV4ThreatHit"}, "response": {"$ref": "GoogleProtobufEmpty"}}}}, "threatListUpdates": {"methods": {"fetch": {"description": "Fetches the most recent threat list updates. A client can request updates for multiple lists at once.", "flatPath": "v4/threatListUpdates:fetch", "httpMethod": "POST", "id": "safebrowsing.threatListUpdates.fetch", "parameterOrder": [], "parameters": {}, "path": "v4/threatListUpdates:fetch", "request": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequest"}, "response": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponse"}}}}, "threatLists": {"methods": {"list": {"description": "Lists the Safe Browsing threat lists available for download.", "flatPath": "v4/threatLists", "httpMethod": "GET", "id": "safebrowsing.threatLists.list", "parameterOrder": [], "parameters": {}, "path": "v4/threatLists", "response": {"$ref": "GoogleSecuritySafebrowsingV4ListThreatListsResponse"}}}}, "threatMatches": {"methods": {"find": {"description": "Finds the threat entries that match the Safe Browsing lists.", "flatPath": "v4/threatMatches:find", "httpMethod": "POST", "id": "safebrowsing.threatMatches.find", "parameterOrder": [], "parameters": {}, "path": "v4/threatMatches:find", "request": {"$ref": "GoogleSecuritySafebrowsingV4FindThreatMatchesRequest"}, "response": {"$ref": "GoogleSecuritySafebrowsingV4FindThreatMatchesResponse"}}}}}, "revision": "20240630", "rootUrl": "https://safebrowsing.googleapis.com/", "schemas": {"GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleSecuritySafebrowsingV4Checksum": {"description": "The expected state of a client's local database.", "id": "GoogleSecuritySafebrowsingV4Checksum", "properties": {"sha256": {"description": "The SHA256 hash of the client state; that is, of the sorted list of all hashes present in the database.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ClientInfo": {"description": "The client metadata associated with Safe Browsing API requests.", "id": "GoogleSecuritySafebrowsingV4ClientInfo", "properties": {"clientId": {"description": "A client ID that (hopefully) uniquely identifies the client implementation of the Safe Browsing API.", "type": "string"}, "clientVersion": {"description": "The version of the client implementation.", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequest": {"description": "Describes a Safe Browsing API update request. Clients can request updates for multiple lists in a single request. The server may not respond to all requests, if the server has no updates for that list. NOTE: Field index 2 is unused. NEXT: 5", "id": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequest", "properties": {"client": {"$ref": "GoogleSecuritySafebrowsingV4ClientInfo", "description": "The client metadata."}, "listUpdateRequests": {"description": "The requested threat list updates.", "items": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequest"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequest": {"description": "A single list update request.", "id": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequest", "properties": {"constraints": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequestConstraints", "description": "The constraints associated with this request."}, "platformType": {"description": "The type of platform at risk by entries present in the list.", "enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "state": {"description": "The current state of the client for the requested list (the encrypted client state that was received from the last successful list update).", "format": "byte", "type": "string"}, "threatEntryType": {"description": "The types of entries present in the list.", "enum": ["THREAT_ENTRY_TYPE_UNSPECIFIED", "URL", "EXECUTABLE", "IP_RANGE", "CHROME_EXTENSION", "FILENAME", "CERT"], "enumDescriptions": ["Unspecified.", "A URL.", "An executable program.", "An IP range.", "Chrome extension.", "Filename.", "CERT"], "type": "string"}, "threatType": {"description": "The type of threat posed by entries present in the list.", "enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequestConstraints": {"description": "The constraints for this update.", "id": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesRequestListUpdateRequestConstraints", "properties": {"deviceLocation": {"description": "A client's physical location, expressed as a ISO 31166-1 alpha-2 region code.", "type": "string"}, "language": {"description": "Requests the lists for a specific language. Expects ISO 639 alpha-2 format.", "type": "string"}, "maxDatabaseEntries": {"description": "Sets the maximum number of entries that the client is willing to have in the local database for the specified list. This should be a power of 2 between 2**10 and 2**20. If zero, no database size limit is set.", "format": "int32", "type": "integer"}, "maxUpdateEntries": {"description": "The maximum size in number of entries. The update will not contain more entries than this value. This should be a power of 2 between 2**10 and 2**20. If zero, no update size limit is set.", "format": "int32", "type": "integer"}, "region": {"description": "Requests the list for a specific geographic location. If not set the server may pick that value based on the user's IP address. Expects ISO 3166-1 alpha-2 format.", "type": "string"}, "supportedCompressions": {"description": "The compression types supported by the client.", "items": {"enum": ["COMPRESSION_TYPE_UNSPECIFIED", "RAW", "RICE"], "enumDescriptions": ["Unknown.", "Raw, uncompressed data.", "Rice-Golomb encoded data."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponse": {"id": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponse", "properties": {"listUpdateResponses": {"description": "The list updates requested by the clients. The number of responses here may be less than the number of requests sent by clients. This is the case, for example, if the server has no updates for a particular list.", "items": {"$ref": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponseListUpdateResponse"}, "type": "array"}, "minimumWaitDuration": {"description": "The minimum duration the client must wait before issuing any update request. If this field is not set clients may update as soon as they want.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponseListUpdateResponse": {"description": "An update to an individual list.", "id": "GoogleSecuritySafebrowsingV4FetchThreatListUpdatesResponseListUpdateResponse", "properties": {"additions": {"description": "A set of entries to add to a local threat type's list. Repeated to allow for a combination of compressed and raw data to be sent in a single response.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntrySet"}, "type": "array"}, "checksum": {"$ref": "GoogleSecuritySafebrowsingV4Checksum", "description": "The expected SHA256 hash of the client state; that is, of the sorted list of all hashes present in the database after applying the provided update. If the client state doesn't match the expected state, the client must disregard this update and retry later."}, "newClientState": {"description": "The new client state, in encrypted format. Opaque to clients.", "format": "byte", "type": "string"}, "platformType": {"description": "The platform type for which data is returned.", "enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "removals": {"description": "A set of entries to remove from a local threat type's list. In practice, this field is empty or contains exactly one ThreatEntrySet.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntrySet"}, "type": "array"}, "responseType": {"description": "The type of response. This may indicate that an action is required by the client when the response is received.", "enum": ["RESPONSE_TYPE_UNSPECIFIED", "PARTIAL_UPDATE", "FULL_UPDATE"], "enumDescriptions": ["Unknown.", "Partial updates are applied to the client's existing local database.", "Full updates replace the client's entire local database. This means that either the client was seriously out-of-date or the client is believed to be corrupt."], "type": "string"}, "threatEntryType": {"description": "The format of the threats.", "enum": ["THREAT_ENTRY_TYPE_UNSPECIFIED", "URL", "EXECUTABLE", "IP_RANGE", "CHROME_EXTENSION", "FILENAME", "CERT"], "enumDescriptions": ["Unspecified.", "A URL.", "An executable program.", "An IP range.", "Chrome extension.", "Filename.", "CERT"], "type": "string"}, "threatType": {"description": "The threat type for which data is returned.", "enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FindFullHashesRequest": {"description": "Request to return full hashes matched by the provided hash prefixes.", "id": "GoogleSecuritySafebrowsingV4FindFullHashesRequest", "properties": {"apiClient": {"$ref": "GoogleSecuritySafebrowsingV4ClientInfo", "description": "Client metadata associated with callers of higher-level APIs built on top of the client's implementation."}, "client": {"$ref": "GoogleSecuritySafebrowsingV4ClientInfo", "description": "The client metadata."}, "clientStates": {"description": "The current client states for each of the client's local threat lists.", "items": {"format": "byte", "type": "string"}, "type": "array"}, "threatInfo": {"$ref": "GoogleSecuritySafebrowsingV4ThreatInfo", "description": "The lists and hashes to be checked."}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FindFullHashesResponse": {"id": "GoogleSecuritySafebrowsingV4FindFullHashesResponse", "properties": {"matches": {"description": "The full hashes that matched the requested prefixes.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatMatch"}, "type": "array"}, "minimumWaitDuration": {"description": "The minimum duration the client must wait before issuing any find hashes request. If this field is not set, clients can issue a request as soon as they want.", "format": "google-duration", "type": "string"}, "negativeCacheDuration": {"description": "For requested entities that did not match the threat list, how long to cache the response.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FindThreatMatchesRequest": {"description": "Request to check entries against lists.", "id": "GoogleSecuritySafebrowsingV4FindThreatMatchesRequest", "properties": {"client": {"$ref": "GoogleSecuritySafebrowsingV4ClientInfo", "description": "The client metadata."}, "threatInfo": {"$ref": "GoogleSecuritySafebrowsingV4ThreatInfo", "description": "The lists and entries to be checked for matches."}}, "type": "object"}, "GoogleSecuritySafebrowsingV4FindThreatMatchesResponse": {"id": "GoogleSecuritySafebrowsingV4FindThreatMatchesResponse", "properties": {"matches": {"description": "The threat list matches.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatMatch"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ListThreatListsResponse": {"id": "GoogleSecuritySafebrowsingV4ListThreatListsResponse", "properties": {"threatLists": {"description": "The lists available for download by the client.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatListDescriptor"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4RawHashes": {"description": "The uncompressed threat entries in hash format of a particular prefix length. Hashes can be anywhere from 4 to 32 bytes in size. A large majority are 4 bytes, but some hashes are lengthened if they collide with the hash of a popular URL. Used for sending ThreatEntrySet to clients that do not support compression, or when sending non-4-byte hashes to clients that do support compression.", "id": "GoogleSecuritySafebrowsingV4RawHashes", "properties": {"prefixSize": {"description": "The number of bytes for each prefix encoded below. This field can be anywhere from 4 (shortest prefix) to 32 (full SHA256 hash).", "format": "int32", "type": "integer"}, "rawHashes": {"description": "The hashes, in binary format, concatenated into one long string. Hashes are sorted in lexicographic order. For JSON API users, hashes are base64-encoded.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4RawIndices": {"description": "A set of raw indices to remove from a local list.", "id": "GoogleSecuritySafebrowsingV4RawIndices", "properties": {"indices": {"description": "The indices to remove from a lexicographically-sorted local list.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4RiceDeltaEncoding": {"description": "The Rice-Golomb encoded data. Used for sending compressed 4-byte hashes or compressed removal indices.", "id": "GoogleSecuritySafebrowsingV4RiceDeltaEncoding", "properties": {"encodedData": {"description": "The encoded deltas that are encoded using the Golomb-Rice coder.", "format": "byte", "type": "string"}, "firstValue": {"description": "The offset of the first entry in the encoded data, or, if only a single integer was encoded, that single integer's value. If the field is empty or missing, assume zero.", "format": "int64", "type": "string"}, "numEntries": {"description": "The number of entries that are delta encoded in the encoded data. If only a single integer was encoded, this will be zero and the single value will be stored in `first_value`.", "format": "int32", "type": "integer"}, "riceParameter": {"description": "The Golomb-Rice parameter, which is a number between 2 and 28. This field is missing (that is, zero) if `num_entries` is zero.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatEntry": {"description": "An individual threat; for example, a malicious URL or its hash representation. Only one of these fields should be set.", "id": "GoogleSecuritySafebrowsingV4ThreatEntry", "properties": {"digest": {"description": "The digest of an executable in SHA256 format. The API supports both binary and hex digests. For JSON requests, digests are base64-encoded.", "format": "byte", "type": "string"}, "hash": {"description": "A hash prefix, consisting of the most significant 4-32 bytes of a SHA256 hash. This field is in binary format. For JSON requests, hashes are base64-encoded.", "format": "byte", "type": "string"}, "url": {"description": "A URL.", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatEntryMetadata": {"description": "The metadata associated with a specific threat entry. The client is expected to know the metadata key/value pairs associated with each threat type.", "id": "GoogleSecuritySafebrowsingV4ThreatEntryMetadata", "properties": {"entries": {"description": "The metadata entries.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntryMetadataMetadataEntry"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatEntryMetadataMetadataEntry": {"description": "A single metadata entry.", "id": "GoogleSecuritySafebrowsingV4ThreatEntryMetadataMetadataEntry", "properties": {"key": {"description": "The metadata entry key. For JSON requests, the key is base64-encoded.", "format": "byte", "type": "string"}, "value": {"description": "The metadata entry value. For JSON requests, the value is base64-encoded.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatEntrySet": {"description": "A set of threats that should be added or removed from a client's local database.", "id": "GoogleSecuritySafebrowsingV4ThreatEntrySet", "properties": {"compressionType": {"description": "The compression type for the entries in this set.", "enum": ["COMPRESSION_TYPE_UNSPECIFIED", "RAW", "RICE"], "enumDescriptions": ["Unknown.", "Raw, uncompressed data.", "Rice-Golomb encoded data."], "type": "string"}, "rawHashes": {"$ref": "GoogleSecuritySafebrowsingV4RawHashes", "description": "The raw SHA256-formatted entries."}, "rawIndices": {"$ref": "GoogleSecuritySafebrowsingV4RawIndices", "description": "The raw removal indices for a local list."}, "riceHashes": {"$ref": "GoogleSecuritySafebrowsingV4RiceDeltaEncoding", "description": "The encoded 4-byte prefixes of SHA256-formatted entries, using a Golomb-Rice encoding. The hashes are converted to uint32, sorted in ascending order, then delta encoded and stored as encoded_data."}, "riceIndices": {"$ref": "GoogleSecuritySafebrowsingV4RiceDeltaEncoding", "description": "The encoded local, lexicographically-sorted list indices, using a Golomb-Rice encoding. Used for sending compressed removal indices. The removal indices (uint32) are sorted in ascending order, then delta encoded and stored as encoded_data."}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatHit": {"id": "GoogleSecuritySafebrowsingV4ThreatHit", "properties": {"clientInfo": {"$ref": "GoogleSecuritySafebrowsingV4ClientInfo", "description": "Client-reported identification."}, "entry": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntry", "description": "The threat entry responsible for the hit. Full hash should be reported for hash-based hits."}, "platformType": {"description": "The platform type reported.", "enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "resources": {"description": "The resources related to the threat hit.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatHitThreatSource"}, "type": "array"}, "threatType": {"description": "The threat type reported.", "enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}, "userInfo": {"$ref": "GoogleSecuritySafebrowsingV4ThreatHitUserInfo", "description": "Details about the user that encountered the threat."}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatHitThreatSource": {"description": "A single resource related to a threat hit.", "id": "GoogleSecuritySafebrowsingV4ThreatHitThreatSource", "properties": {"referrer": {"description": "Referrer of the resource. Only set if the referrer is available.", "type": "string"}, "remoteIp": {"description": "The remote IP of the resource in ASCII format. Either IPv4 or IPv6.", "type": "string"}, "type": {"description": "The type of source reported.", "enum": ["THREAT_SOURCE_TYPE_UNSPECIFIED", "MATCHING_URL", "TAB_URL", "TAB_REDIRECT", "TAB_RESOURCE"], "enumDescriptions": ["Unknown.", "The URL that matched the threat list (for which GetFullHash returned a valid hash).", "The final top-level URL of the tab that the client was browsing when the match occurred.", "A redirect URL that was fetched before hitting the final TAB_URL.", "A resource loaded within the final TAB_URL."], "type": "string"}, "url": {"description": "The URL of the resource.", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatHitUserInfo": {"description": "Details about the user that encountered the threat.", "id": "GoogleSecuritySafebrowsingV4ThreatHitUserInfo", "properties": {"regionCode": {"description": "The UN M.49 region code associated with the user's location.", "type": "string"}, "userId": {"description": "Unique user identifier defined by the client.", "format": "byte", "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatInfo": {"description": "The information regarding one or more threats that a client submits when checking for matches in threat lists.", "id": "GoogleSecuritySafebrowsingV4ThreatInfo", "properties": {"platformTypes": {"description": "The platform types to be checked.", "items": {"enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "type": "array"}, "threatEntries": {"description": "The threat entries to be checked.", "items": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntry"}, "type": "array"}, "threatEntryTypes": {"description": "The entry types to be checked.", "items": {"enum": ["THREAT_ENTRY_TYPE_UNSPECIFIED", "URL", "EXECUTABLE", "IP_RANGE", "CHROME_EXTENSION", "FILENAME", "CERT"], "enumDescriptions": ["Unspecified.", "A URL.", "An executable program.", "An IP range.", "Chrome extension.", "Filename.", "CERT"], "type": "string"}, "type": "array"}, "threatTypes": {"description": "The threat types to be checked.", "items": {"enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatListDescriptor": {"description": "Describes an individual threat list. A list is defined by three parameters: the type of threat posed, the type of platform targeted by the threat, and the type of entries in the list.", "id": "GoogleSecuritySafebrowsingV4ThreatListDescriptor", "properties": {"platformType": {"description": "The platform type targeted by the list's entries.", "enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "threatEntryType": {"description": "The entry types contained in the list.", "enum": ["THREAT_ENTRY_TYPE_UNSPECIFIED", "URL", "EXECUTABLE", "IP_RANGE", "CHROME_EXTENSION", "FILENAME", "CERT"], "enumDescriptions": ["Unspecified.", "A URL.", "An executable program.", "An IP range.", "Chrome extension.", "Filename.", "CERT"], "type": "string"}, "threatType": {"description": "The threat type posed by the list's entries.", "enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}}, "type": "object"}, "GoogleSecuritySafebrowsingV4ThreatMatch": {"description": "A match when checking a threat entry in the Safe Browsing threat lists.", "id": "GoogleSecuritySafebrowsingV4ThreatMatch", "properties": {"cacheDuration": {"description": "The cache lifetime for the returned match. Clients must not cache this response for more than this duration to avoid false positives.", "format": "google-duration", "type": "string"}, "platformType": {"description": "The platform type matching this threat.", "enum": ["PLATFORM_TYPE_UNSPECIFIED", "WINDOWS", "LINUX", "ANDROID", "OSX", "IOS", "ANY_PLATFORM", "ALL_PLATFORMS", "CHROME"], "enumDescriptions": ["Unknown platform.", "Threat posed to Windows.", "Threat posed to Linux.", "Threat posed to Android.", "Threat posed to OS X.", "Threat posed to iOS.", "Threat posed to at least one of the defined platforms.", "Threat posed to all defined platforms.", "Threat posed to Chrome."], "type": "string"}, "threat": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntry", "description": "The threat matching this threat."}, "threatEntryMetadata": {"$ref": "GoogleSecuritySafebrowsingV4ThreatEntryMetadata", "description": "Optional metadata associated with this threat."}, "threatEntryType": {"description": "The threat entry type matching this threat.", "enum": ["THREAT_ENTRY_TYPE_UNSPECIFIED", "URL", "EXECUTABLE", "IP_RANGE", "CHROME_EXTENSION", "FILENAME", "CERT"], "enumDescriptions": ["Unspecified.", "A URL.", "An executable program.", "An IP range.", "Chrome extension.", "Filename.", "CERT"], "type": "string"}, "threatType": {"description": "The threat type matching this threat.", "enum": ["THREAT_TYPE_UNSPECIFIED", "MALWARE", "SOCIAL_ENGINEERING", "UNWANTED_SOFTWARE", "POTENTIALLY_HARMFUL_APPLICATION", "SOCIAL_ENGINEERING_INTERNAL", "API_ABUSE", "MALICIOUS_BINARY", "CSD_WHITELIST", "CSD_DOWNLOAD_WHITELIST", "CLIENT_INCIDENT", "CLIENT_INCIDENT_WHITELIST", "APK_MALWARE_OFFLINE", "SUBRESOURCE_FILTER", "SUSPICIOUS", "TRICK_TO_BILL", "HIGH_CONFIDENCE_ALLOWLIST", "ACCURACY_TIPS"], "enumDescriptions": ["Unknown.", "Malware threat type.", "Social engineering threat type.", "Unwanted software threat type.", "Potentially harmful application threat type.", "Social engineering threat type for internal use.", "API abuse threat type.", "Malicious binary threat type.", "Client side detection whitelist threat type.", "Client side download detection whitelist threat type.", "Client incident threat type.", "Whitelist used when detecting client incident threats.", "List used for offline APK checks in PAM.", "Patterns to be used for activating the subresource filter.", "Entities that are suspected to present a threat.", "Trick-to-bill threat type.", "URL expressions that are very likely to be safe.", "An experimental threat type related to Jigsaw. See https://jigsaw.google.com/."], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Safe Browsing API", "version": "v4", "version_module": true}