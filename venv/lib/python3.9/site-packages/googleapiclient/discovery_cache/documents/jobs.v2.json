{"batchPath": "batch", "mtlsRootUrl": "https://jobs.mtls.googleapis.com/", "rootUrl": "https://jobs.googleapis.com/", "basePath": "", "icons": {"x32": "http://www.google.com/images/icons/product/search-32.gif", "x16": "http://www.google.com/images/icons/product/search-16.gif"}, "revision": "20200929", "canonicalName": "Cloud Talent Solution", "id": "jobs:v2", "version_module": true, "schemas": {"GoogleCloudTalentV4CompensationInfoCompensationRange": {"type": "object", "description": "Compensation range.", "properties": {"minCompensation": {"$ref": "Money", "description": "The minimum amount of compensation. If left empty, the value is set to zero and the currency code is set to match the currency code of max_compensation."}, "maxCompensation": {"$ref": "Money", "description": "The maximum amount of compensation. If left empty, the value is set to a maximal compensation value and the currency code is set to match the currency code of min_compensation."}}, "id": "GoogleCloudTalentV4CompensationInfoCompensationRange"}, "MatchingJob": {"description": "Output only. Job entry with metadata inside SearchJobsResponse.", "id": "MatchingJob", "type": "object", "properties": {"jobSummary": {"type": "string", "description": "A summary of the job with core information that's displayed on the search results listing page."}, "job": {"$ref": "Job", "description": "Job resource that matches the specified SearchJobsRequest."}, "commuteInfo": {"$ref": "CommuteInfo", "description": "Commute information which is generated based on specified CommutePreference."}, "searchTextSnippet": {"type": "string", "description": "Contains snippets of text from the Job.description and similar fields that most closely match a search query's keywords, if available. All HTML tags in the original fields are stripped when returned in this field, and matching query keywords are enclosed in HTML bold tags."}, "jobTitleSnippet": {"description": "Contains snippets of text from the Job.job_title field most closely matching a search query's keywords, if available. The matching query keywords are enclosed in HTML bold tags.", "type": "string"}}}, "GoogleCloudTalentV4BatchOperationMetadata": {"id": "GoogleCloudTalentV4BatchOperationMetadata", "type": "object", "description": "Metadata used for long running operations returned by CTS batch APIs. It's used to replace google.longrunning.Operation.metadata.", "properties": {"updateTime": {"format": "google-datetime", "description": "The time when the batch operation status is updated. The metadata and the update_time is refreshed every minute otherwise cached data is returned.", "type": "string"}, "endTime": {"description": "The time when the batch operation is finished and google.longrunning.Operation.done is set to `true`.", "type": "string", "format": "google-datetime"}, "state": {"enumDescriptions": ["Default value.", "The batch operation is being prepared for processing.", "The batch operation is actively being processed.", "The batch operation is processed, and at least one item has been successfully processed.", "The batch operation is done and no item has been successfully processed.", "The batch operation is in the process of cancelling after google.longrunning.Operations.CancelOperation is called.", "The batch operation is done after google.longrunning.Operations.CancelOperation is called. Any items processed before cancelling are returned in the response."], "enum": ["STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "SUCCEEDED", "FAILED", "CANCELLING", "CANCELLED"], "description": "The state of a long running operation.", "type": "string"}, "successCount": {"description": "Count of successful item(s) inside an operation.", "type": "integer", "format": "int32"}, "createTime": {"description": "The time when the batch operation is created.", "format": "google-datetime", "type": "string"}, "stateDescription": {"description": "More detailed information about operation state.", "type": "string"}, "failureCount": {"type": "integer", "format": "int32", "description": "Count of failed item(s) inside an operation."}, "totalCount": {"format": "int32", "description": "Count of total item(s) inside an operation.", "type": "integer"}}}, "DeleteJobsByFilterRequest": {"type": "object", "properties": {"filter": {"$ref": "Filter", "description": "Required. Restrictions on the scope of the delete request."}, "disableFastProcess": {"description": "Optional. If set to true, this call waits for all processing steps to complete before the job is cleaned up. Otherwise, the call returns while some steps are still taking place asynchronously, hence faster.", "type": "boolean"}}, "description": "Deprecated. Use BatchDeleteJobsRequest instead. Input only. Delete job by filter request. The job typically becomes unsearchable within 10 seconds, but it may take up to 5 minutes.", "id": "DeleteJobsByFilterRequest"}, "GoogleCloudTalentV4Job": {"type": "object", "properties": {"jobStartTime": {"type": "string", "description": "The start timestamp of the job in UTC time zone. Typically this field is used for contracting engagements. Invalid timestamps are ignored.", "format": "google-datetime"}, "processingOptions": {"$ref": "GoogleCloudTalentV4JobProcessingOptions", "description": "Options for job processing."}, "companyDisplayName": {"description": "Output only. Display name of the company listing the job.", "readOnly": true, "type": "string"}, "promotionValue": {"description": "A promotion value of the job, as determined by the client. The value determines the sort order of the jobs returned when searching for jobs using the featured jobs search call, with higher promotional values being returned first and ties being resolved by relevance sort. Only the jobs with a promotionValue >0 are returned in a FEATURED_JOB_SEARCH. Default value is 0, and negative values are treated as 0.", "format": "int32", "type": "integer"}, "applicationInfo": {"$ref": "GoogleCloudTalentV4JobApplicationInfo", "description": "Job application information."}, "postingRegion": {"description": "The job PostingRegion (for example, state, country) throughout which the job is available. If this field is set, a LocationFilter in a search query within the job region finds this job posting if an exact location match isn't specified. If this field is set to PostingRegion.NATION or PostingRegion.ADMINISTRATIVE_AREA, setting job Job.addresses to the same location level as this field is strongly recommended.", "enumDescriptions": ["If the region is unspecified, the job is only returned if it matches the LocationFilter.", "In addition to exact location matching, job posting is returned when the LocationFilter in the search query is in the same administrative area as the returned job posting. For example, if a `ADMINISTRATIVE_AREA` job is posted in \"CA, USA\", it's returned if LocationFilter has \"Mountain View\". Administrative area refers to top-level administrative subdivision of this country. For example, US state, IT region, UK constituent nation and JP prefecture.", "In addition to exact location matching, job is returned when LocationFilter in search query is in the same country as this job. For example, if a `NATION_WIDE` job is posted in \"USA\", it's returned if LocationFilter has 'Mountain View'.", "Job allows employees to work remotely (telecommute). If locations are provided with this value, the job is considered as having a location, but telecommuting is allowed."], "enum": ["POSTING_REGION_UNSPECIFIED", "ADMINISTRATIVE_AREA", "NATION", "TELECOMMUTE"], "type": "string"}, "postingPublishTime": {"type": "string", "format": "google-datetime", "description": "The timestamp this job posting was most recently published. The default value is the time the request arrives at the server. Invalid timestamps are ignored."}, "addresses": {"description": "Strongly recommended for the best service experience. Location(s) where the employer is looking to hire for this job posting. Specifying the full street address(es) of the hiring location enables better API results, especially job searches by commute time. At most 50 locations are allowed for best search performance. If a job has more locations, it is suggested to split it into multiple jobs with unique requisition_ids (e.g. 'ReqA' becomes 'ReqA-1', 'ReqA-2', and so on.) as multiple jobs with the same company, language_code and requisition_id are not allowed. If the original requisition_id must be preserved, a custom field should be used for storage. It is also suggested to group the locations that close to each other in the same job for better search experience. The maximum number of allowed characters is 500.", "type": "array", "items": {"type": "string"}}, "derivedInfo": {"description": "Output only. Derived details about the job posting.", "readOnly": true, "$ref": "GoogleCloudTalentV4JobDerivedInfo"}, "jobLevel": {"type": "string", "enum": ["JOB_LEVEL_UNSPECIFIED", "ENTRY_LEVEL", "EXPERIENCED", "MANAGER", "DIRECTOR", "EXECUTIVE"], "description": "The experience level associated with the job, such as \"Entry Level\".", "enumDescriptions": ["The default value if the level isn't specified.", "Entry-level individual contributors, typically with less than 2 years of experience in a similar role. Includes interns.", "Experienced individual contributors, typically with 2+ years of experience in a similar role.", "Entry- to mid-level managers responsible for managing a team of people.", "Senior-level managers responsible for managing teams of managers.", "Executive-level managers and above, including C-level positions."]}, "department": {"type": "string", "description": "The department or functional area within the company with the open position. The maximum number of allowed characters is 255."}, "company": {"type": "string", "description": "Required. The resource name of the company listing the job. The format is \"projects/{project_id}/tenants/{tenant_id}/companies/{company_id}\". For example, \"projects/foo/tenants/bar/companies/baz\"."}, "postingExpireTime": {"format": "google-datetime", "type": "string", "description": "Strongly recommended for the best service experience. The expiration timestamp of the job. After this timestamp, the job is marked as expired, and it no longer appears in search results. The expired job can't be listed by the ListJobs API, but it can be retrieved with the GetJob API or updated with the UpdateJob API or deleted with the DeleteJob API. An expired job can be updated and opened again by using a future expiration timestamp. Updating an expired job fails if there is another existing open job with same company, language_code and requisition_id. The expired jobs are retained in our system for 90 days. However, the overall expired job count cannot exceed 3 times the maximum number of open jobs over previous 7 days. If this threshold is exceeded, expired jobs are cleaned out in order of earliest expire time. Expired jobs are no longer accessible after they are cleaned out. Invalid timestamps are ignored, and treated as expire time not provided. If the timestamp is before the instant request is made, the job is treated as expired immediately on creation. This kind of job can not be updated. And when creating a job with past timestamp, the posting_publish_time must be set before posting_expire_time. The purpose of this feature is to allow other objects, such as Application, to refer a job that didn't exist in the system prior to becoming expired. If you want to modify a job that was expired on creation, delete it and create a new one. If this value isn't provided at the time of job creation or is invalid, the job posting expires after 30 days from the job's creation time. For example, if the job was created on 2017/01/01 13:00AM UTC with an unspecified expiration date, the job expires after 2017/01/31 13:00AM UTC. If this value isn't provided on job update, it depends on the field masks set by UpdateJobRequest.update_mask. If the field masks include job_end_time, or the masks are empty meaning that every field is updated, the job posting expires after 30 days from the job's last update time. Otherwise the expiration date isn't updated."}, "qualifications": {"type": "string", "description": "A description of the qualifications required to perform the job. The use of this field is recommended as an alternative to using the more general description field. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 10,000."}, "postingUpdateTime": {"type": "string", "description": "Output only. The timestamp when this job posting was last updated.", "format": "google-datetime", "readOnly": true}, "visibility": {"description": "Deprecated. The job is only visible to the owner. The visibility of the job. Defaults to Visibility.ACCOUNT_ONLY if not specified.", "type": "string", "enum": ["VISIBILITY_UNSPECIFIED", "ACCOUNT_ONLY", "SHARED_WITH_GOOGLE", "SHARED_WITH_PUBLIC"], "enumDescriptions": ["Default value.", "The resource is only visible to the GCP account who owns it.", "The resource is visible to the owner and may be visible to other applications and processes at Google.", "The resource is visible to the owner and may be visible to all other API clients."]}, "postingCreateTime": {"format": "google-datetime", "type": "string", "readOnly": true, "description": "Output only. The timestamp when this job posting was created."}, "degreeTypes": {"type": "array", "description": "The desired education degrees for the job, such as Bachelors, Masters.", "items": {"type": "string", "enumDescriptions": ["Default value. Represents no degree, or early childhood education. Maps to ISCED code 0. Ex) Kindergarten", "Primary education which is typically the first stage of compulsory education. ISCED code 1. Ex) Elementary school", "Lower secondary education; First stage of secondary education building on primary education, typically with a more subject-oriented curriculum. ISCED code 2. Ex) Middle school", "Middle education; Second/final stage of secondary education preparing for tertiary education and/or providing skills relevant to employment. Usually with an increased range of subject options and streams. ISCED code 3. Ex) High school", "Adult Remedial Education; Programmes providing learning experiences that build on secondary education and prepare for labour market entry and/or tertiary education. The content is broader than secondary but not as complex as tertiary education. ISCED code 4.", "Associate's or equivalent; Short first tertiary programmes that are typically practically-based, occupationally-specific and prepare for labour market entry. These programmes may also provide a pathway to other tertiary programmes. ISCED code 5.", "Bachelor's or equivalent; Programmes designed to provide intermediate academic and/or professional knowledge, skills and competencies leading to a first tertiary degree or equivalent qualification. ISCED code 6.", "Master's or equivalent; Programmes designed to provide advanced academic and/or professional knowledge, skills and competencies leading to a second tertiary degree or equivalent qualification. ISCED code 7.", "Doctoral or equivalent; Programmes designed primarily to lead to an advanced research qualification, usually concluding with the submission and defense of a substantive dissertation of publishable quality based on original research. ISCED code 8."], "enum": ["DEGREE_TYPE_UNSPECIFIED", "PRIMARY_EDUCATION", "LOWER_SECONDARY_EDUCATION", "UPPER_SECONDARY_EDUCATION", "ADULT_REMEDIAL_EDUCATION", "ASSOCIATES_OR_EQUIVALENT", "BACHELORS_OR_EQUIVALENT", "MASTERS_OR_EQUIVALENT", "DOCTORAL_OR_EQUIVALENT"]}}, "name": {"type": "string", "description": "Required during job update. The resource name for the job. This is generated by the service when a job is created. The format is \"projects/{project_id}/tenants/{tenant_id}/jobs/{job_id}\". For example, \"projects/foo/tenants/bar/jobs/baz\". Use of this field in job queries and API calls is preferred over the use of requisition_id since this value is unique."}, "jobBenefits": {"description": "The benefits included with the job.", "items": {"enum": ["JOB_BENEFIT_UNSPECIFIED", "CHILD_CARE", "DENTAL", "DOMESTIC_PARTNER", "FLEXIBLE_HOURS", "MEDICAL", "LIFE_INSURANCE", "PARENTAL_LEAVE", "RETIREMENT_PLAN", "SICK_DAYS", "VACATION", "VISION"], "type": "string", "enumDescriptions": ["Default value if the type isn't specified.", "The job includes access to programs that support child care, such as daycare.", "The job includes dental services covered by a dental insurance plan.", "The job offers specific benefits to domestic partners.", "The job allows for a flexible work schedule.", "The job includes health services covered by a medical insurance plan.", "The job includes a life insurance plan provided by the employer or available for purchase by the employee.", "The job allows for a leave of absence to a parent to care for a newborn child.", "The job includes a workplace retirement plan provided by the employer or available for purchase by the employee.", "The job allows for paid time off due to illness.", "The job includes paid time off for vacation.", "The job includes vision services covered by a vision insurance plan."]}, "type": "array"}, "employmentTypes": {"type": "array", "description": "The employment type(s) of a job, for example, full time or part time.", "items": {"enum": ["EMPLOYMENT_TYPE_UNSPECIFIED", "FULL_TIME", "PART_TIME", "CONTRACTOR", "CONTRACT_TO_HIRE", "TEMPORARY", "INTERN", "VOLUNTEER", "PER_DIEM", "FLY_IN_FLY_OUT", "OTHER_EMPLOYMENT_TYPE"], "enumDescriptions": ["The default value if the employment type isn't specified.", "The job requires working a number of hours that constitute full time employment, typically 40 or more hours per week.", "The job entails working fewer hours than a full time job, typically less than 40 hours a week.", "The job is offered as a contracted, as opposed to a salaried employee, position.", "The job is offered as a contracted position with the understanding that it's converted into a full-time position at the end of the contract. Jobs of this type are also returned by a search for EmploymentType.CONTRACTOR jobs.", "The job is offered as a temporary employment opportunity, usually a short-term engagement.", "The job is a fixed-term opportunity for students or entry-level job seekers to obtain on-the-job training, typically offered as a summer position.", "The is an opportunity for an individual to volunteer, where there's no expectation of compensation for the provided services.", "The job requires an employee to work on an as-needed basis with a flexible schedule.", "The job involves employing people in remote areas and flying them temporarily to the work site instead of relocating employees and their families permanently.", "The job does not fit any of the other listed types."], "type": "string"}}, "responsibilities": {"type": "string", "description": "A description of job responsibilities. The use of this field is recommended as an alternative to using the more general description field. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 10,000."}, "customAttributes": {"description": "A map of fields to hold both filterable and non-filterable custom job attributes that are not covered by the provided structured fields. The keys of the map are strings up to 64 bytes and must match the pattern: a-zA-Z*. For example, key0LikeThis or KEY_1_LIKE_THIS. At most 100 filterable and at most 100 unfilterable keys are supported. For filterable `string_values`, across all keys at most 200 values are allowed, with each string no more than 255 characters. For unfilterable `string_values`, the maximum total size of `string_values` across all keys is 50KB.", "type": "object", "additionalProperties": {"$ref": "GoogleCloudTalentV4CustomAttribute"}}, "languageCode": {"type": "string", "description": "The language of the posting. This field is distinct from any requirements for fluency that are associated with the job. Language codes must be in BCP-47 format, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47){: class=\"external\" target=\"_blank\" }. If this field is unspecified and Job.description is present, detected language code based on Job.description is assigned, otherwise defaults to 'en_US'."}, "title": {"description": "Required. The title of the job, such as \"Software Engineer\" The maximum number of allowed characters is 500.", "type": "string"}, "jobEndTime": {"type": "string", "description": "The end timestamp of the job. Typically this field is used for contracting engagements. Invalid timestamps are ignored.", "format": "google-datetime"}, "incentives": {"type": "string", "description": "A description of bonus, commission, and other compensation incentives associated with the job not including salary or pay. The maximum number of allowed characters is 10,000."}, "description": {"description": "Required. The description of the job, which typically includes a multi-paragraph description of the company and related information. Separate fields are provided on the job object for responsibilities, qualifications, and other job characteristics. Use of these separate job fields is recommended. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 100,000.", "type": "string"}, "requisitionId": {"type": "string", "description": "Required. The requisition ID, also referred to as the posting ID, is assigned by the client to identify a job. This field is intended to be used by clients for client identification and tracking of postings. A job isn't allowed to be created if there is another job with the same company, language_code and requisition_id. The maximum number of allowed characters is 255."}, "compensationInfo": {"$ref": "GoogleCloudTalentV4CompensationInfo", "description": "Job compensation information (a.k.a. \"pay rate\") i.e., the compensation that will paid to the employee."}}, "id": "GoogleCloudTalentV4Job", "description": "A Job resource represents a job posting (also referred to as a \"job listing\" or \"job requisition\"). A job belongs to a Company, which is the hiring entity responsible for the job."}, "GoogleCloudTalentV4BatchDeleteJobsResponse": {"id": "GoogleCloudTalentV4BatchDeleteJobsResponse", "description": "The result of JobService.BatchDeleteJobs. It's used to replace google.longrunning.Operation.response in case of success.", "properties": {"jobResults": {"description": "List of job mutation results from a batch delete operation. It can change until operation status is FINISHED, FAILED or CANCELLED.", "items": {"$ref": "GoogleCloudTalentV4JobResult"}, "type": "array"}}, "type": "object"}, "DeviceInfo": {"id": "DeviceInfo", "properties": {"id": {"description": "Optional. A device-specific ID. The ID must be a unique identifier that distinguishes the device from other devices.", "type": "string"}, "deviceType": {"enumDescriptions": ["The device type isn't specified.", "A desktop web browser, such as, Chrome, Firefox, Safari, or Internet Explorer)", "A mobile device web browser, such as a phone or tablet with a Chrome browser.", "An Android device native application.", "An iOS device native application.", "A bot, as opposed to a device operated by human beings, such as a web crawler.", "Other devices types."], "type": "string", "description": "Optional. Type of the device.", "enum": ["DEVICE_TYPE_UNSPECIFIED", "WEB", "MOBILE_WEB", "ANDROID", "IOS", "BOT", "OTHER"]}}, "type": "object", "description": "Input only. Device information collected from the job seeker, candidate, or other entity conducting the job search. Providing this information improves the quality of the search results across devices."}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON representation for `Empty` is empty JSON object `{}`.", "id": "Empty", "properties": {}, "type": "object"}, "CustomAttributeHistogramResult": {"description": "Output only. Custom attribute histogram result.", "properties": {"key": {"description": "Stores the key of custom attribute the histogram is performed on.", "type": "string"}, "stringValueHistogramResult": {"additionalProperties": {"type": "integer", "format": "int32"}, "description": "Stores a map from the values of string custom field associated with `key` to the number of jobs with that value in this histogram result.", "type": "object"}, "longValueHistogramResult": {"$ref": "NumericBucketingResult", "description": "Stores bucketed histogram counting result or min/max values for custom attribute long values associated with `key`."}}, "id": "CustomAttributeHistogramResult", "type": "object"}, "ResponseMetadata": {"id": "ResponseMetadata", "type": "object", "properties": {"experimentIdList": {"type": "array", "description": "Identifiers for the versions of the search algorithm used during this API invocation if multiple algorithms are used. The default value is empty. For search response only.", "items": {"format": "int32", "type": "integer"}}, "mode": {"description": "For search response only. Indicates the mode of a performed search.", "type": "string", "enumDescriptions": ["The mode of the search method isn't specified.", "The job search doesn't include support for featured jobs.", "The job search matches only against featured jobs (jobs with a promotionValue > 0). This method doesn't return any jobs having a promotionValue <= 0. The search results order is determined by the promotionValue (jobs with a higher promotionValue are returned higher up in the search results), with relevance being used as a tiebreaker.", "Deprecated. Please use the SearchJobsForAlert API. The job search matches against jobs suited to email notifications."], "enum": ["SEARCH_MODE_UNSPECIFIED", "JOB_SEARCH", "FEATURED_JOB_SEARCH", "EMAIL_ALERT_SEARCH"]}, "requestId": {"description": "A unique id associated with this call. This id is logged for tracking purposes.", "type": "string"}}, "description": "Output only. Additional information returned to client, such as debugging information."}, "GoogleCloudTalentV4CustomAttribute": {"properties": {"stringValues": {"type": "array", "description": "Exactly one of string_values or long_values must be specified. This field is used to perform a string match (`CASE_SENSITIVE_MATCH` or `CASE_INSENSITIVE_MATCH`) search. For filterable `string_value`s, a maximum total number of 200 values is allowed, with each `string_value` has a byte size of no more than 500B. For unfilterable `string_values`, the maximum total byte size of unfilterable `string_values` is 50KB. Empty string isn't allowed.", "items": {"type": "string"}}, "filterable": {"type": "boolean", "description": "If the `filterable` flag is true, the custom field values may be used for custom attribute filters JobQuery.custom_attribute_filter. If false, these values may not be used for custom attribute filters. Default is false."}, "longValues": {"description": "Exactly one of string_values or long_values must be specified. This field is used to perform number range search. (`EQ`, `GT`, `GE`, `LE`, `LT`) over filterable `long_value`. Currently at most 1 long_values is supported.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "keywordSearchable": {"type": "boolean", "description": "If the `keyword_searchable` flag is true, the keywords in custom fields are searchable by keyword match. If false, the values are not searchable by keyword match. Default is false."}}, "description": "Custom attribute values that are either filterable or non-filterable.", "id": "GoogleCloudTalentV4CustomAttribute", "type": "object"}, "CompleteQueryResponse": {"description": "Output only. Response of auto-complete query.", "id": "CompleteQueryResponse", "type": "object", "properties": {"metadata": {"description": "Additional information for the API invocation, such as the request tracking id.", "$ref": "ResponseMetadata"}, "completionResults": {"items": {"$ref": "CompletionResult"}, "description": "Results of the matching job/company candidates.", "type": "array"}}}, "CustomAttribute": {"properties": {"longValue": {"format": "int64", "description": "Optional but at least one of string_values or long_value must be specified. This field is used to perform number range search. (`EQ`, `GT`, `GE`, `LE`, `LT`) over filterable `long_value`. For `long_value`, a value between Long.MIN and Long.MAX is allowed.", "type": "string"}, "filterable": {"description": "Optional. If the `filterable` flag is true, custom field values are searchable. If false, values are not searchable. Default is false.", "type": "boolean"}, "stringValues": {"description": "Optional but at least one of string_values or long_value must be specified. This field is used to perform a string match (`CASE_SENSITIVE_MATCH` or `CASE_INSENSITIVE_MATCH`) search. For filterable `string_values`, a maximum total number of 200 values is allowed, with each `string_value` has a byte size of no more than 255B. For unfilterable `string_values`, the maximum total byte size of unfilterable `string_values` is 50KB. Empty strings are not allowed.", "$ref": "StringValues"}}, "type": "object", "id": "CustomAttribute", "description": "Custom attribute values that are either filterable or non-filterable."}, "JobProcessingOptions": {"type": "object", "description": "Input only. Options for job processing.", "properties": {"disableStreetAddressResolution": {"type": "boolean", "description": "Optional. If set to `true`, the service does not attempt to resolve a more precise address for the job."}, "htmlSanitization": {"enumDescriptions": ["Default value.", "Disables sanitization on HTML input.", "Sanitizes HTML input, only accepts bold, italic, ordered list, and unordered list markup tags."], "type": "string", "description": "Optional. Option for job HTML content sanitization. Applied fields are: * description * applicationInstruction * incentives * qualifications * responsibilities HTML tags in these fields may be stripped if sanitiazation is not disabled. Defaults to HtmlSanitization.SIMPLE_FORMATTING_ONLY.", "enum": ["HTML_SANITIZATION_UNSPECIFIED", "HTML_SANITIZATION_DISABLED", "SIMPLE_FORMATTING_ONLY"]}}, "id": "JobProcessingOptions"}, "CompensationHistogramResult": {"id": "CompensationHistogramResult", "type": "object", "description": "Output only. Compensation based histogram result.", "properties": {"result": {"$ref": "NumericBucketingResult", "description": "Histogram result."}, "type": {"type": "string", "enumDescriptions": ["Default value. Invalid.", "Histogram by job's base compensation. See CompensationEntry for definition of base compensation.", "Histogram by job's annualized base compensation. See CompensationEntry for definition of annualized base compensation.", "Histogram by job's annualized total compensation. See CompensationEntry for definition of annualized total compensation."], "description": "Type of the request, corresponding to CompensationHistogramRequest.type.", "enum": ["COMPENSATION_HISTOGRAM_REQUEST_TYPE_UNSPECIFIED", "BASE", "ANNUALIZED_BASE", "ANNUALIZED_TOTAL"]}}}, "GetHistogramResponse": {"properties": {"metadata": {"$ref": "ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "results": {"items": {"$ref": "HistogramResult"}, "description": "The Histogram results.", "type": "array"}}, "description": "Deprecated. Use SearchJobsRequest.histogram_facets instead to make a single call with both search and histogram. Output only. The response of the GetHistogram method.", "type": "object", "id": "GetHistogramResponse"}, "CompensationEntry": {"id": "CompensationEntry", "type": "object", "properties": {"unit": {"enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_UNIT.", "Hourly.", "Daily.", "Weekly", "Monthly.", "Yearly.", "One time.", "Other compensation units."], "description": "Optional. Frequency of the specified amount. Default is CompensationUnit.COMPENSATION_UNIT_UNSPECIFIED.", "enum": ["COMPENSATION_UNIT_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME", "OTHER_COMPENSATION_UNIT"], "type": "string"}, "description": {"description": "Optional. Compensation description. For example, could indicate equity terms or provide additional context to an estimated bonus.", "type": "string"}, "type": {"type": "string", "enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_TYPE.", "Base compensation: Refers to the fixed amount of money paid to an employee by an employer in return for work performed. Base compensation does not include benefits, bonuses or any other potential compensation from an employer.", "Bonus.", "Signing bonus.", "Equity.", "Profit sharing.", "Commission.", "Tips.", "Other compensation type."], "enum": ["COMPENSATION_TYPE_UNSPECIFIED", "BASE", "BONUS", "SIGNING_BONUS", "EQUITY", "PROFIT_SHARING", "COMMISSIONS", "TIPS", "OTHER_COMPENSATION_TYPE"], "description": "Required. Compensation type."}, "amount": {"$ref": "Money", "description": "Optional. Compensation amount."}, "range": {"$ref": "CompensationRange", "description": "Optional. Compensation range."}, "expectedUnitsPerYear": {"format": "double", "description": "Optional. Expected number of units paid each year. If not specified, when Job.employment_types is FULLTIME, a default value is inferred based on unit. Default values: - HOURLY: 2080 - DAILY: 260 - WEEKLY: 52 - MONTHLY: 12 - ANNUAL: 1", "type": "number"}}, "description": "A compensation entry that represents one component of compensation, such as base pay, bonus, or other compensation type. Annualization: One compensation entry can be annualized if - it contains valid amount or range. - and its expected_units_per_year is set or can be derived. Its annualized range is determined as (amount or range) times expected_units_per_year."}, "LatLng": {"type": "object", "description": "An object representing a latitude/longitude pair. This is expressed as a pair of doubles representing degrees latitude and degrees longitude. Unless specified otherwise, this must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "LatLng", "properties": {"latitude": {"type": "number", "description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "type": "number", "format": "double"}}}, "CompensationHistogramRequest": {"properties": {"type": {"type": "string", "enumDescriptions": ["Default value. Invalid.", "Histogram by job's base compensation. See CompensationEntry for definition of base compensation.", "Histogram by job's annualized base compensation. See CompensationEntry for definition of annualized base compensation.", "Histogram by job's annualized total compensation. See CompensationEntry for definition of annualized total compensation."], "enum": ["COMPENSATION_HISTOGRAM_REQUEST_TYPE_UNSPECIFIED", "BASE", "ANNUALIZED_BASE", "ANNUALIZED_TOTAL"], "description": "Required. Type of the request, representing which field the histogramming should be performed over. A single request can only specify one histogram of each `CompensationHistogramRequestType`."}, "bucketingOption": {"description": "Required. Numeric histogram options, like buckets, whether include min or max value.", "$ref": "NumericBucketingOption"}}, "description": "Input only. Compensation based histogram request.", "id": "CompensationHistogramRequest", "type": "object"}, "GoogleCloudTalentV4JobDerivedInfo": {"description": "Derived details about the job posting.", "type": "object", "id": "GoogleCloudTalentV4JobDerivedInfo", "properties": {"locations": {"type": "array", "description": "Structured locations of the job, resolved from Job.addresses. locations are exactly matched to Job.addresses in the same order.", "items": {"$ref": "GoogleCloudTalentV4Location"}}, "jobCategories": {"description": "Job categories derived from Job.title and Job.description.", "items": {"type": "string", "enumDescriptions": ["The default value if the category isn't specified.", "An accounting and finance job, such as an Accountant.", "An administrative and office job, such as an Administrative Assistant.", "An advertising and marketing job, such as Marketing Manager.", "An animal care job, such as Veterinarian.", "An art, fashion, or design job, such as Designer.", "A business operations job, such as Business Operations Manager.", "A cleaning and facilities job, such as Custodial Staff.", "A computer and IT job, such as Systems Administrator.", "A construction job, such as General Laborer.", "A customer service job, such s Cashier.", "An education job, such as School Teacher.", "An entertainment and travel job, such as Flight Attendant.", "A farming or outdoor job, such as Park Ranger.", "A healthcare job, such as Registered Nurse.", "A human resources job, such as Human Resources Director.", "An installation, maintenance, or repair job, such as Electrician.", "A legal job, such as Law Clerk.", "A management job, often used in conjunction with another category, such as Store Manager.", "A manufacturing or warehouse job, such as Assembly Technician.", "A media, communications, or writing job, such as Media Relations.", "An oil, gas or mining job, such as Offshore Driller.", "A personal care and services job, such as <PERSON> Stylist.", "A protective services job, such as Security Guard.", "A real estate job, such as Buyer's Agent.", "A restaurant and hospitality job, such as Restaurant Server.", "A sales and/or retail job, such Sales Associate.", "A science and engineering job, such as Lab Technician.", "A social services or non-profit job, such as Case Worker.", "A sports, fitness, or recreation job, such as Personal Trainer.", "A transportation or logistics job, such as Truck Driver."], "enum": ["JOB_CATEGORY_UNSPECIFIED", "ACCOUNTING_AND_FINANCE", "ADMINISTRATIVE_AND_OFFICE", "ADVERTISING_AND_MARKETING", "ANIMAL_CARE", "ART_FASHION_AND_DESIGN", "BUSINESS_OPERATIONS", "CLEANING_AND_FACILITIES", "COMPUTER_AND_IT", "CONSTRUCTION", "CUSTOMER_SERVICE", "EDUCATION", "ENTERTAINMENT_AND_TRAVEL", "FARMING_AND_OUTDOORS", "HEALTHCARE", "HUMAN_RESOURCES", "INSTALLATION_MAINTENANCE_AND_REPAIR", "LEGAL", "MANAGEMENT", "MANUFACTURING_AND_WAREHOUSE", "MEDIA_COMMUNICATIONS_AND_WRITING", "OIL_GAS_AND_MINING", "PERSONAL_CARE_AND_SERVICES", "PROTECTIVE_SERVICES", "REAL_ESTATE", "RESTAURANT_AND_HOSPITALITY", "SALES_AND_RETAIL", "SCIENCE_AND_ENGINEERING", "SOCIAL_SERVICES_AND_NON_PROFIT", "SPORTS_FITNESS_AND_RECREATION", "TRANSPORTATION_AND_LOGISTICS"]}, "type": "array"}}}, "JobFilters": {"description": "Input only. Deprecated. Use JobQuery instead. The filters required to perform a search query or histogram.", "id": "JobFilters", "type": "object", "properties": {"languageCodes": {"type": "array", "items": {"type": "string"}, "description": "Optional. This filter specifies the locale of jobs to search against, for example, \"en-US\". If a value is not specified, the search results may contain jobs in any locale. Language codes should be in BCP-47 format, for example, \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47). At most 10 language code filters are allowed."}, "extendedCompensationFilter": {"$ref": "ExtendedCompensationFilter", "description": "Deprecated. Always use compensation_filter. Optional. This search filter is applied only to Job.extended_compensation_info. For example, if the filter is specified as \"Hourly job with per-hour compensation > $15\", only jobs that meet these criteria are searched. If a filter is not defined, all open jobs are searched."}, "categories": {"type": "array", "items": {"type": "string", "enumDescriptions": ["The default value if the category is not specified.", "An accounting and finance job, such as an Accountant.", "And administrative and office job, such as an Administrative Assistant.", "An advertising and marketing job, such as Marketing Manager.", "An animal care job, such as Veterinarian.", "An art, fashion, or design job, such as Designer.", "A business operations job, such as Business Operations Manager.", "A cleaning and facilities job, such as Custodial Staff.", "A computer and IT job, such as Systems Administrator.", "A construction job, such as General Laborer.", "A customer service job, such s Cashier.", "An education job, such as School Teacher.", "An entertainment and travel job, such as Flight Attendant.", "A farming or outdoor job, such as Park Ranger.", "A healthcare job, such as Registered Nurse.", "A human resources job, such as Human Resources Director.", "An installation, maintenance, or repair job, such as Electrician.", "A legal job, such as Law Clerk.", "A management job, often used in conjunction with another category, such as Store Manager.", "A manufacturing or warehouse job, such as Assembly Technician.", "A media, communications, or writing job, such as Media Relations.", "An oil, gas or mining job, such as Offshore Driller.", "A personal care and services job, such as <PERSON> Stylist.", "A protective services job, such as Security Guard.", "A real estate job, such as Buyer's Agent.", "A restaurant and hospitality job, such as Restaurant Server.", "A sales and/or retail job, such Sales Associate.", "A science and engineering job, such as Lab Technician.", "A social services or non-profit job, such as Case Worker.", "A sports, fitness, or recreation job, such as Personal Trainer.", "A transportation or logistics job, such as Truck Driver."], "enum": ["JOB_CATEGORY_UNSPECIFIED", "ACCOUNTING_AND_FINANCE", "ADMINISTRATIVE_AND_OFFICE", "ADVERTISING_AND_MARKETING", "ANIMAL_CARE", "ART_FASHION_AND_DESIGN", "BUSINESS_OPERATIONS", "CLEANING_AND_FACILITIES", "COMPUTER_AND_IT", "CONSTRUCTION", "CUSTOMER_SERVICE", "EDUCATION", "ENTERTAINMENT_AND_TRAVEL", "FARMING_AND_OUTDOORS", "HEALTHCARE", "HUMAN_RESOURCES", "INSTALLATION_MAINTENANCE_AND_REPAIR", "LEGAL", "MANAGEMENT", "MANUFACTURING_AND_WAREHOUSE", "MEDIA_COMMUNICATIONS_AND_WRITING", "OIL_GAS_AND_MINING", "PERSONAL_CARE_AND_SERVICES", "PROTECTIVE_SERVICES", "REAL_ESTATE", "RESTAURANT_AND_HOSPITALITY", "SALES_AND_RETAIL", "SCIENCE_AND_ENGINEERING", "SOCIAL_SERVICES_AND_NON_PROFIT", "SPORTS_FITNESS_AND_RECREATION", "TRANSPORTATION_AND_LOGISTICS"]}, "description": "Optional. The category filter specifies the categories of jobs to search against. See Category for more information. If a value is not specified, jobs from any category are searched against. If multiple values are specified, jobs from any of the specified categories are searched against."}, "customAttributeFilter": {"type": "string", "description": "Optional. This filter specifies a structured syntax to match against the Job.custom_attributes that are marked as `filterable`. The syntax for this expression is a subset of Google SQL syntax. Supported operators are: =, !=, <, <=, >, >= where the left of the operator is a custom field key and the right of the operator is a number or string (surrounded by quotes) value. Supported functions are LOWER() to perform case insensitive match and EMPTY() to filter on the existence of a key. Boolean expressions (AND/OR/NOT) are supported up to 3 levels of nesting (For example, \"((A AND B AND C) OR NOT D) AND E\"), and there can be a maximum of 100 comparisons/functions in the expression. The expression must be < 3000 bytes in length. Sample Query: (key1 = \"TEST\" OR LOWER(key1)=\"test\" OR NOT EMPTY(key1)) AND key2 > 100"}, "query": {"description": "Optional. The query filter contains the keywords that match against the job title, description, and location fields. The maximum query size is 255 bytes/characters.", "type": "string"}, "disableSpellCheck": {"description": "Optional. This flag controls the spell-check feature. If false, the service attempts to correct a misspelled query, for example, \"enginee\" is corrected to \"engineer\". Defaults to false: a spell check is performed.", "type": "boolean"}, "publishDateRange": {"description": "Optional. Jobs published within a range specified by this filter are searched against, for example, DateRange.PAST_MONTH. If a value is not specified, all open jobs are searched against regardless of the date they were published.", "type": "string", "enumDescriptions": ["Default value: Filtering on time is not performed.", "The past 24 hours", "The past week (7 days)", "The past month (30 days)", "The past year (365 days)", "The past 3 days"], "enum": ["DATE_RANGE_UNSPECIFIED", "PAST_24_HOURS", "PAST_WEEK", "PAST_MONTH", "PAST_YEAR", "PAST_3_DAYS"]}, "commuteFilter": {"description": "Optional. Allows filtering jobs by commute time with different travel methods (e.g. driving or public transit). Note: this only works with COMMUTE MODE. When specified, [JobFilters.location_filters] will be ignored. Currently we do not support sorting by commute time.", "$ref": "CommutePreference"}, "tenantJobOnly": {"description": "Deprecated. Do not use this field. This flag controls whether the job search should be restricted to jobs owned by the current user. Defaults to false where all jobs accessible to the user are searched against.", "type": "boolean"}, "locationFilters": {"items": {"$ref": "LocationFilter"}, "description": "Optional. The location filter specifies geo-regions containing the jobs to search against. See LocationFilter for more information. If a location value is not specified, jobs are retrieved from all locations. If multiple values are specified, jobs are retrieved from any of the specified locations. If different values are specified for the LocationFilter.distance_in_miles parameter, the maximum provided distance is used for all locations. At most 5 location filters are allowed.", "type": "array"}, "employmentTypes": {"items": {"type": "string", "enum": ["EMPLOYMENT_TYPE_UNSPECIFIED", "FULL_TIME", "PART_TIME", "CONTRACTOR", "TEMPORARY", "INTERN", "VOLUNTEER", "PER_DIEM", "CONTRACT_TO_HIRE", "FLY_IN_FLY_OUT", "OTHER"], "enumDescriptions": ["The default value if the employment type is not specified.", "The job requires working a number of hours that constitute full time employment, typically 40 or more hours per week.", "The job entails working fewer hours than a full time job, typically less than 40 hours a week.", "The job is offered as a contracted, as opposed to a salaried employee, position.", "The job is offered as a temporary employment opportunity, usually a short-term engagement.", "The job is a fixed-term opportunity for students or entry-level job seekers to obtain on-the-job training, typically offered as a summer position.", "The is an opportunity for an individual to volunteer, where there is no expectation of compensation for the provided services.", "The job requires an employee to work on an as-needed basis with a flexible schedule.", "The job is offered as a contracted position with the understanding that it is converted into a full-time position at the end of the contract. Jobs of this type are also returned by a search for EmploymentType.CONTRACTOR jobs.", "The job involves employing people in remote areas and flying them temporarily to the work site instead of relocating employees and their families permanently.", "The job does not fit any of the other listed types."]}, "description": "Optional. The employment type filter specifies the employment type of jobs to search against, such as EmploymentType.FULL_TIME. If a value is not specified, jobs in the search results include any employment type. If multiple values are specified, jobs in the search results include any of the specified employment types.", "type": "array"}, "companyNames": {"type": "array", "description": "Optional. The company names filter specifies the company entities to search against. If a value is not specified, jobs are searched for against all companies. If multiple values are specified, jobs are searched against the specified companies. At most 20 company filters are allowed.", "items": {"type": "string"}}, "compensationFilter": {"$ref": "CompensationFilter", "description": "Optional. This search filter is applied only to Job.compensation_info. For example, if the filter is specified as \"Hourly job with per-hour compensation > $15\", only jobs that meet this criteria are searched. If a filter is not defined, all open jobs are searched."}, "companyTitles": {"description": "Optional. This filter specifies the exact company titles of jobs to search against. If a value is not specified, jobs within the search results can be associated with any company. If multiple values are specified, jobs within the search results may be associated with any of the specified companies. At most 20 company title filters are allowed.", "type": "array", "items": {"type": "string"}}, "customFieldFilters": {"description": "Deprecated. Use custom_attribute_filter instead. Optional. This filter specifies searching against custom field values. See Job.filterable_custom_fields for information. The key value specifies a number between 1-20 (the service supports 20 custom fields) corresponding to the desired custom field map value. If an invalid key is provided or specified together with custom_attribute_filter, an error is thrown.", "type": "object", "additionalProperties": {"$ref": "CustomFieldFilter"}}}}, "SearchJobsResponse": {"id": "SearchJobsResponse", "type": "object", "properties": {"estimatedTotalSize": {"format": "int64", "description": "An estimation of the number of jobs that match the specified query. This number is not guaranteed to be accurate. For accurate results, seenenable_precise_result_size.", "type": "string"}, "appliedCommuteFilter": {"description": "The commute filter the service applied to the specified query. This information is only available when query has a valid CommutePreference.", "$ref": "CommutePreference"}, "appliedJobLocationFilters": {"description": "The location filters that the service applied to the specified query. If any filters are lat-lng based, the JobLocation.location_type is JobLocation.LocationType#LOCATION_TYPE_UNSPECIFIED.", "type": "array", "items": {"$ref": "JobLocation"}}, "histogramResults": {"description": "The histogram results that match specified SearchJobsRequest.HistogramFacets.", "$ref": "HistogramResults"}, "matchingJobs": {"type": "array", "items": {"$ref": "MatchingJob"}, "description": "The Job entities that match the specified SearchJobsRequest."}, "totalSize": {"description": "The precise result count, which is available only if the client set enable_precise_result_size to `true` or if the response is the last page of results. Otherwise, the value will be `-1`.", "type": "string", "format": "int64"}, "spellResult": {"description": "The spell checking result, and correction.", "$ref": "SpellingCorrection"}, "jobView": {"enum": ["JOB_VIEW_UNSPECIFIED", "SMALL", "MINIMAL", "FULL"], "type": "string", "description": "Corresponds to SearchJobsRequest.job_view.", "enumDescriptions": ["Default value.", "A small view of the job, with the following attributes in the search results: Job.name, Job.requisition_id, Job.job_title, Job.company_name, Job.job_locations, Job.description, Job.visibility. Note: Job.description is deprecated. It is scheduled to be removed from MatchingJob.Job objects in the SearchJobsResponse results on 12/31/2018.", "A minimal view of the job, with the following attributes in the search results: Job.name, Job.requisition_id, Job.job_title, Job.company_name, Job.job_locations.", "All available attributes are included in the search results. Note: [Job.description, Job.responsibilities, Job.qualifications and Job.incentives are deprecated. These fields are scheduled to be removed from MatchingJob.Job objects in the SearchJobsResponse results on 12/31/2018. See the alternative MatchingJob.search_text_snippet and MatchingJob.job_summary fields."]}, "numJobsFromBroadenedQuery": {"format": "int32", "type": "integer", "description": "If query broadening is enabled, we may append additional results from the broadened query. This number indicates how many of the jobs returned in the jobs field are from the broadened query. These results are always at the end of the jobs list. In particular, a value of 0 means all the jobs in the jobs list are from the original (without broadening) query. If this field is non-zero, subsequent requests with offset after this result set should contain all broadened results."}, "nextPageToken": {"description": "The token that specifies the starting position of the next page of results. This field is empty if there are no more results.", "type": "string"}, "metadata": {"description": "Additional information for the API invocation, such as the request tracking id.", "$ref": "ResponseMetadata"}}, "description": "Output only. Response for SearchJob method."}, "HistogramFacets": {"properties": {"simpleHistogramFacets": {"type": "array", "description": "Optional. Specifies the simple type of histogram facets, for example, `COMPANY_SIZE`, `EMPLOYMENT_TYPE` etc. This field is equivalent to GetHistogramRequest.", "items": {"enum": ["JOB_FIELD_UNSPECIFIED", "COMPANY_ID", "EMPLOYMENT_TYPE", "COMPANY_SIZE", "DATE_PUBLISHED", "CUSTOM_FIELD_1", "CUSTOM_FIELD_2", "CUSTOM_FIELD_3", "CUSTOM_FIELD_4", "CUSTOM_FIELD_5", "CUSTOM_FIELD_6", "CUSTOM_FIELD_7", "CUSTOM_FIELD_8", "CUSTOM_FIELD_9", "CUSTOM_FIELD_10", "CUSTOM_FIELD_11", "CUSTOM_FIELD_12", "CUSTOM_FIELD_13", "CUSTOM_FIELD_14", "CUSTOM_FIELD_15", "CUSTOM_FIELD_16", "CUSTOM_FIELD_17", "CUSTOM_FIELD_18", "CUSTOM_FIELD_19", "CUSTOM_FIELD_20", "EDUCATION_LEVEL", "EXPERIENCE_LEVEL", "ADMIN1", "COUNTRY", "CITY", "LOCALE", "LANGUAGE", "CATEGORY", "CITY_COORDINATE", "ADMIN1_COUNTRY", "COMPANY_TITLE", "COMPANY_DISPLAY_NAME", "BASE_COMPENSATION_UNIT"], "type": "string", "enumDescriptions": ["The default value if search type is not specified.", "Filter by the company id field.", "Filter by the employment type field, such as `FULL_TIME` or `PART_TIME`.", "Filter by the company size type field, such as `BIG`, `SMALL` or `BIGGER`.", "Filter by the date published field. Values are stringified with TimeRange, for example, TimeRange.PAST_MONTH.", "Filter by custom field 1.", "Filter by custom field 2.", "Filter by custom field 3.", "Filter by custom field 4.", "Filter by custom field 5.", "Filter by custom field 6.", "Filter by custom field 7.", "Filter by custom field 8.", "Filter by custom field 9.", "Filter by custom field 10.", "Filter by custom field 11.", "Filter by custom field 12.", "Filter by custom field 13.", "Filter by custom field 14.", "Filter by custom field 15.", "Filter by custom field 16.", "Filter by custom field 17.", "Filter by custom field 18.", "Filter by custom field 19.", "Filter by custom field 20.", "Filter by the required education level of the job.", "Filter by the required experience level of the job.", "Filter by Admin1, which is a global placeholder for referring to state, province, or the particular term a country uses to define the geographic structure below the country level. Examples include states codes such as \"CA\", \"IL\", \"NY\", and provinces, such as \"BC\".", "Filter by the country code of job, such as US, JP, FR.", "Filter by the \"city name\", \"Admin1 code\", for example, \"Mountain View, CA\" or \"New York, NY\".", "Filter by the locale field of a job, such as \"en-US\", \"fr-FR\". This is the BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47).", "Filter by the language code portion of the locale field, such as \"en\" or \"fr\".", "Filter by the Category.", "Filter by the city center GPS coordinate (latitude and longitude), for example, 37.4038522,-122.0987765. Since the coordinates of a city center can change, clients may need to refresh them periodically.", "A combination of state or province code with a country code. This field differs from `JOB_ADMIN1`, which can be used in multiple countries.", "Deprecated. Use COMPANY_DISPLAY_NAME instead. Company display name.", "Company display name.", "Base compensation unit."]}}, "compensationHistogramFacets": {"description": "Optional. Specifies compensation field-based histogram requests. Duplicate values of CompensationHistogramRequest.type are not allowed.", "type": "array", "items": {"$ref": "CompensationHistogramRequest"}}, "customAttributeHistogramFacets": {"items": {"$ref": "CustomAttributeHistogramRequest"}, "description": "Optional. Specifies the custom attributes histogram requests. Duplicate values of CustomAttributeHistogramRequest.key are not allowed.", "type": "array"}}, "id": "HistogramFacets", "description": "Input only. Histogram facets to be specified in SearchJobsRequest.", "type": "object"}, "ListCompanyJobsResponse": {"type": "object", "properties": {"metadata": {"description": "Additional information for the API invocation, such as the request tracking id.", "$ref": "ResponseMetadata"}, "totalSize": {"description": "The total number of open jobs. The result will be empty if ListCompanyJobsRequest.include_jobs_count is not enabled or if no open jobs are available.", "type": "string", "format": "int64"}, "jobs": {"description": "The Jobs for a given company. The maximum number of items returned is based on the limit field provided in the request.", "type": "array", "items": {"$ref": "Job"}}, "nextPageToken": {"description": "A token to retrieve the next page of results.", "type": "string"}}, "id": "ListCompanyJobsResponse", "description": "Deprecated. Use ListJobsResponse instead. Output only. The List jobs response object."}, "GoogleCloudTalentV4JobProcessingOptions": {"properties": {"htmlSanitization": {"type": "string", "enumDescriptions": ["Default value.", "Disables sanitization on HTML input.", "Sanitizes HTML input, only accepts bold, italic, ordered list, and unordered list markup tags."], "description": "Option for job HTML content sanitization. Applied fields are: * description * applicationInfo.instruction * incentives * qualifications * responsibilities HTML tags in these fields may be stripped if sanitiazation isn't disabled. Defaults to HtmlSanitization.SIMPLE_FORMATTING_ONLY.", "enum": ["HTML_SANITIZATION_UNSPECIFIED", "HTML_SANITIZATION_DISABLED", "SIMPLE_FORMATTING_ONLY"]}, "disableStreetAddressResolution": {"description": "If set to `true`, the service does not attempt to resolve a more precise address for the job.", "type": "boolean"}}, "type": "object", "description": "Options for job processing.", "id": "GoogleCloudTalentV4JobProcessingOptions"}, "CustomAttributeHistogramRequest": {"properties": {"longValueHistogramBucketingOption": {"$ref": "NumericBucketingOption", "description": "Optional. Specifies buckets used to perform a range histogram on Job's filterable long custom field values, or min/max value requirements."}, "key": {"type": "string", "description": "Required. Specifies the custom field key to perform a histogram on. If specified without `long_value_histogram_bucketing_option`, histogram on string values of the given `key` is triggered, otherwise histogram is performed on long values."}, "stringValueHistogram": {"description": "Optional. If set to true, the response will include the histogram value for each key as a string.", "type": "boolean"}}, "id": "CustomAttributeHistogramRequest", "description": "Custom attributes histogram request. An error will be thrown if neither string_value_histogram or long_value_histogram_bucketing_option has been defined.", "type": "object"}, "NumericBucketingOption": {"id": "NumericBucketingOption", "properties": {"bucketBounds": {"items": {"type": "number", "format": "double"}, "description": "Required. Two adjacent values form a histogram bucket. Values should be in ascending order. For example, if [5, 10, 15] are provided, four buckets are created: (-inf, 5), 5, 10), [10, 15), [15, inf). At most 20 [buckets_bound is supported.", "type": "array"}, "requiresMinMax": {"description": "Optional. If set to true, the histogram result includes minimum/maximum value of the numeric field.", "type": "boolean"}}, "description": "Input only. Use this field to specify bucketing option for the histogram search response.", "type": "object"}, "GoogleCloudTalentV4CompensationInfo": {"type": "object", "id": "GoogleCloudTalentV4CompensationInfo", "properties": {"entries": {"type": "array", "items": {"$ref": "GoogleCloudTalentV4CompensationInfoCompensationEntry"}, "description": "Job compensation information. At most one entry can be of type CompensationInfo.CompensationType.BASE, which is referred as **base compensation entry** for the job."}, "annualizedBaseCompensationRange": {"readOnly": true, "$ref": "GoogleCloudTalentV4CompensationInfoCompensationRange", "description": "Output only. Annualized base compensation range. Computed as base compensation entry's CompensationEntry.amount times CompensationEntry.expected_units_per_year. See CompensationEntry for explanation on compensation annualization."}, "annualizedTotalCompensationRange": {"description": "Output only. Annualized total compensation range. Computed as all compensation entries' CompensationEntry.amount times CompensationEntry.expected_units_per_year. See CompensationEntry for explanation on compensation annualization.", "readOnly": true, "$ref": "GoogleCloudTalentV4CompensationInfoCompensationRange"}}, "description": "Job compensation details."}, "HistogramResult": {"id": "HistogramResult", "type": "object", "description": "Output only. Result of a histogram call. The response contains the histogram map for the search type specified by HistogramResult.field. The response is a map of each filter value to the corresponding count of jobs for that filter.", "properties": {"searchType": {"type": "string", "enum": ["JOB_FIELD_UNSPECIFIED", "COMPANY_ID", "EMPLOYMENT_TYPE", "COMPANY_SIZE", "DATE_PUBLISHED", "CUSTOM_FIELD_1", "CUSTOM_FIELD_2", "CUSTOM_FIELD_3", "CUSTOM_FIELD_4", "CUSTOM_FIELD_5", "CUSTOM_FIELD_6", "CUSTOM_FIELD_7", "CUSTOM_FIELD_8", "CUSTOM_FIELD_9", "CUSTOM_FIELD_10", "CUSTOM_FIELD_11", "CUSTOM_FIELD_12", "CUSTOM_FIELD_13", "CUSTOM_FIELD_14", "CUSTOM_FIELD_15", "CUSTOM_FIELD_16", "CUSTOM_FIELD_17", "CUSTOM_FIELD_18", "CUSTOM_FIELD_19", "CUSTOM_FIELD_20", "EDUCATION_LEVEL", "EXPERIENCE_LEVEL", "ADMIN1", "COUNTRY", "CITY", "LOCALE", "LANGUAGE", "CATEGORY", "CITY_COORDINATE", "ADMIN1_COUNTRY", "COMPANY_TITLE", "COMPANY_DISPLAY_NAME", "BASE_COMPENSATION_UNIT"], "enumDescriptions": ["The default value if search type is not specified.", "Filter by the company id field.", "Filter by the employment type field, such as `FULL_TIME` or `PART_TIME`.", "Filter by the company size type field, such as `BIG`, `SMALL` or `BIGGER`.", "Filter by the date published field. Values are stringified with TimeRange, for example, TimeRange.PAST_MONTH.", "Filter by custom field 1.", "Filter by custom field 2.", "Filter by custom field 3.", "Filter by custom field 4.", "Filter by custom field 5.", "Filter by custom field 6.", "Filter by custom field 7.", "Filter by custom field 8.", "Filter by custom field 9.", "Filter by custom field 10.", "Filter by custom field 11.", "Filter by custom field 12.", "Filter by custom field 13.", "Filter by custom field 14.", "Filter by custom field 15.", "Filter by custom field 16.", "Filter by custom field 17.", "Filter by custom field 18.", "Filter by custom field 19.", "Filter by custom field 20.", "Filter by the required education level of the job.", "Filter by the required experience level of the job.", "Filter by Admin1, which is a global placeholder for referring to state, province, or the particular term a country uses to define the geographic structure below the country level. Examples include states codes such as \"CA\", \"IL\", \"NY\", and provinces, such as \"BC\".", "Filter by the country code of job, such as US, JP, FR.", "Filter by the \"city name\", \"Admin1 code\", for example, \"Mountain View, CA\" or \"New York, NY\".", "Filter by the locale field of a job, such as \"en-US\", \"fr-FR\". This is the BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47).", "Filter by the language code portion of the locale field, such as \"en\" or \"fr\".", "Filter by the Category.", "Filter by the city center GPS coordinate (latitude and longitude), for example, 37.4038522,-122.0987765. Since the coordinates of a city center can change, clients may need to refresh them periodically.", "A combination of state or province code with a country code. This field differs from `JOB_ADMIN1`, which can be used in multiple countries.", "Deprecated. Use COMPANY_DISPLAY_NAME instead. Company display name.", "Company display name.", "Base compensation unit."], "description": "The Histogram search filters."}, "values": {"description": "A map from the values of field to the number of jobs with that value in this search result. Key: search type (filter names, such as the companyName). Values: the count of jobs that match the filter for this search.", "type": "object", "additionalProperties": {"format": "int32", "type": "integer"}}}}, "GoogleCloudTalentV4CompensationInfoCompensationEntry": {"properties": {"description": {"type": "string", "description": "Compensation description. For example, could indicate equity terms or provide additional context to an estimated bonus."}, "amount": {"$ref": "Money", "description": "Compensation amount."}, "expectedUnitsPerYear": {"type": "number", "description": "Expected number of units paid each year. If not specified, when Job.employment_types is FULLTIME, a default value is inferred based on unit. Default values: - HOURLY: 2080 - DAILY: 260 - WEEKLY: 52 - MONTHLY: 12 - ANNUAL: 1", "format": "double"}, "range": {"$ref": "GoogleCloudTalentV4CompensationInfoCompensationRange", "description": "Compensation range."}, "type": {"description": "Compensation type. Default is CompensationType.COMPENSATION_TYPE_UNSPECIFIED.", "type": "string", "enumDescriptions": ["Default value.", "Base compensation: Refers to the fixed amount of money paid to an employee by an employer in return for work performed. Base compensation does not include benefits, bonuses or any other potential compensation from an employer.", "Bonus.", "Signing bonus.", "Equity.", "Profit sharing.", "Commission.", "Tips.", "Other compensation type."], "enum": ["COMPENSATION_TYPE_UNSPECIFIED", "BASE", "BONUS", "SIGNING_BONUS", "EQUITY", "PROFIT_SHARING", "COMMISSIONS", "TIPS", "OTHER_COMPENSATION_TYPE"]}, "unit": {"type": "string", "description": "Frequency of the specified amount. Default is CompensationUnit.COMPENSATION_UNIT_UNSPECIFIED.", "enumDescriptions": ["Default value.", "Hourly.", "Daily.", "Weekly", "Monthly.", "Yearly.", "One time.", "Other compensation units."], "enum": ["COMPENSATION_UNIT_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME", "OTHER_COMPENSATION_UNIT"]}}, "description": "A compensation entry that represents one component of compensation, such as base pay, bonus, or other compensation type. Annualization: One compensation entry can be annualized if - it contains valid amount or range. - and its expected_units_per_year is set or can be derived. Its annualized range is determined as (amount or range) times expected_units_per_year.", "type": "object", "id": "GoogleCloudTalentV4CompensationInfoCompensationEntry"}, "Status": {"id": "Status", "description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "type": "object", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "message": {"type": "string", "description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client."}, "details": {"items": {"type": "object", "additionalProperties": {"type": "any", "description": "Properties of the object. Contains field @type with type URL."}}, "description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "type": "array"}}}, "BucketRange": {"properties": {"to": {"description": "Ending value of the bucket range.", "format": "double", "type": "number"}, "from": {"description": "Starting value of the bucket range.", "format": "double", "type": "number"}}, "type": "object", "id": "BucketRange", "description": "Represents starting and ending value of a range in double."}, "CommuteInfo": {"id": "CommuteInfo", "description": "Output only. Commute details related to this job.", "type": "object", "properties": {"jobLocation": {"$ref": "JobLocation", "description": "Location used as the destination in the commute calculation."}, "travelDuration": {"type": "string", "format": "google-duration", "description": "The number of seconds required to travel to the job location from the query location. A duration of 0 seconds indicates that the job is not reachable within the requested duration, but was returned as part of an expanded query."}}}, "ExtendedCompensationInfoCompensationEntry": {"description": "Deprecated. See CompensationInfo. A compensation entry that represents one component of compensation, such as base pay, bonus, or other compensation type. Annualization: One compensation entry can be annualized if - it contains valid amount or range. - and its expected_units_per_year is set or can be derived. Its annualized range is determined as (amount or range) times expected_units_per_year.", "properties": {"unit": {"type": "string", "enum": ["EXTENDED_COMPENSATION_UNIT_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME", "OTHER_COMPENSATION_UNIT"], "description": "Optional. Frequency of the specified amount. Default is CompensationUnit.COMPENSATION_UNIT_UNSPECIFIED.", "enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_UNIT.", "Hourly.", "Daily.", "Weekly", "Monthly.", "Yearly.", "One time.", "Other compensation units."]}, "expectedUnitsPerYear": {"description": "Optional. Expected number of units paid each year. If not specified, when Job.employment_types is FULLTIME, a default value is inferred based on unit. Default values: - HOURLY: 2080 - DAILY: 260 - WEEKLY: 52 - MONTHLY: 12 - ANNUAL: 1", "$ref": "ExtendedCompensationInfoDecimal"}, "type": {"description": "Required. Compensation type.", "enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_TYPE.", "Base compensation: Refers to the fixed amount of money paid to an employee by an employer in return for work performed. Base compensation does not include benefits, bonuses or any other potential compensation from an employer.", "Bonus.", "Signing bonus.", "Equity.", "Profit sharing.", "Commission.", "Tips.", "Other compensation type."], "enum": ["EXTENDED_COMPENSATION_TYPE_UNSPECIFIED", "BASE", "BONUS", "SIGNING_BONUS", "EQUITY", "PROFIT_SHARING", "COMMISSIONS", "TIPS", "OTHER_COMPENSATION_TYPE"], "type": "string"}, "range": {"description": "Optional. Compensation range.", "$ref": "ExtendedCompensationInfoCompensationRange"}, "amount": {"description": "Optional. Monetary amount.", "$ref": "ExtendedCompensationInfoDecimal"}, "unspecified": {"description": "Optional. Indicates compensation amount and range are unset.", "type": "boolean"}, "description": {"description": "Optional. Compensation description.", "type": "string"}}, "type": "object", "id": "ExtendedCompensationInfoCompensationEntry"}, "CompensationInfo": {"description": "Job compensation details.", "properties": {"amount": {"$ref": "Money", "description": "Deprecated. Use entries instead. Optional. The amount of compensation or pay for the job. As an alternative, compensation_amount_min and compensation_amount_max may be used to define a range of compensation."}, "entries": {"type": "array", "items": {"$ref": "CompensationEntry"}, "description": "Optional. Job compensation information. At most one entry can be of type CompensationInfo.CompensationType.BASE, which is referred as ** base compensation entry ** for the job."}, "annualizedBaseCompensationRange": {"description": "Output only. Annualized base compensation range. Computed as base compensation entry's CompensationEntry.compensation times CompensationEntry.expected_units_per_year. See CompensationEntry for explanation on compensation annualization.", "$ref": "CompensationRange"}, "type": {"enumDescriptions": ["The default value if the type is not specified.", "The job compensation is quoted by the number of hours worked.", "The job compensation is quoted on an annual basis.", "The job compensation is quoted by project completion.", "The job compensation is quoted based solely on commission.", "The job compensation is not quoted according to the listed compensation options."], "type": "string", "description": "Deprecated. Use entries instead. Optional. Type of job compensation.", "enum": ["JOB_COMPENSATION_TYPE_UNSPECIFIED", "HOURLY", "SALARY", "PER_PROJECT", "COMMISSION", "OTHER_TYPE"]}, "annualizedTotalCompensationRange": {"$ref": "CompensationRange", "description": "Output only. Annualized total compensation range. Computed as all compensation entries' CompensationEntry.compensation times CompensationEntry.expected_units_per_year. See CompensationEntry for explanation on compensation annualization."}, "min": {"description": "Deprecated. Use entries instead. Optional. A lower bound on a range for compensation or pay for the job. The currency type is specified in compensation_amount.", "$ref": "Money"}, "max": {"$ref": "Money", "description": "Deprecated. Use entries instead. Optional. An upper bound on a range for compensation or pay for the job. The currency type is specified in compensation_amount."}}, "type": "object", "id": "CompensationInfo"}, "StringValues": {"id": "StringValues", "type": "object", "description": "Represents array of string values.", "properties": {"values": {"description": "Required. String values.", "items": {"type": "string"}, "type": "array"}}}, "ListCompaniesResponse": {"properties": {"metadata": {"description": "Additional information for the API invocation, such as the request tracking id.", "$ref": "ResponseMetadata"}, "nextPageToken": {"type": "string", "description": "A token to retrieve the next page of results."}, "companies": {"type": "array", "description": "Companies for the current client.", "items": {"$ref": "Company"}}}, "id": "ListCompaniesResponse", "type": "object", "description": "Output only. The List companies response object."}, "CustomFieldFilter": {"type": "object", "id": "CustomFieldFilter", "properties": {"type": {"type": "string", "description": "Optional. The type of filter. Defaults to FilterType.OR.", "enumDescriptions": ["Default value.", "Search for a match with any query.", "Search for a match with all queries.", "Negate the set of filter values for the search."], "enum": ["FILTER_TYPE_UNSPECIFIED", "OR", "AND", "NOT"]}, "queries": {"items": {"type": "string"}, "type": "array", "description": "Required. The query strings for the filter."}}, "description": "Input only. Custom field filter of the search."}, "BucketizedCount": {"id": "BucketizedCount", "type": "object", "properties": {"count": {"description": "Number of jobs whose numeric field value fall into `range`.", "type": "integer", "format": "int32"}, "range": {"$ref": "BucketRange", "description": "Bucket range on which histogram was performed for the numeric field, that is, the count represents number of jobs in this range."}}, "description": "Represents count of jobs within one bucket."}, "NumericBucketingResult": {"description": "Output only. Custom numeric bucketing result.", "properties": {"counts": {"type": "array", "description": "Count within each bucket. Its size is the length of NumericBucketingOption.bucket_bounds plus 1.", "items": {"$ref": "BucketizedCount"}}, "minValue": {"type": "number", "description": "Stores the minimum value of the numeric field. Will be populated only if [NumericBucketingOption.requires_min_max] is set to true.", "format": "double"}, "maxValue": {"type": "number", "format": "double", "description": "Stores the maximum value of the numeric field. Will be populated only if [NumericBucketingOption.requires_min_max] is set to true."}}, "type": "object", "id": "NumericBucketingResult"}, "SearchJobsRequest": {"properties": {"query": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Query used to search against jobs, such as keyword, location filters, etc."}, "filters": {"description": "Deprecated. Use query instead. Optional. Restrictions on the scope of the search request, such as filtering by location.", "$ref": "JobFilters"}, "pageToken": {"description": "Optional. The token specifying the current offset within search results. See SearchJobsResponse.next_page_token for an explanation of how to obtain the next set of query results.", "type": "string"}, "enablePreciseResultSize": {"description": "Optional. Controls if the search job request requires the return of a precise count of the first 300 results. Setting this to `true` ensures consistency in the number of results per page. Best practice is to set this value to true if a client allows users to jump directly to a non-sequential search results page. Enabling this flag may adversely impact performance. Defaults to false.", "type": "boolean"}, "pageSize": {"description": "Optional. A limit on the number of jobs returned in the search results. Increasing this value above the default value of 10 can increase search response time. The value can be between 1 and 100.", "type": "integer", "format": "int32"}, "histogramFacets": {"description": "Optional. Restrictions on what fields to perform histogram on, such as `COMPANY_SIZE` etc.", "$ref": "HistogramFacets"}, "mode": {"description": "Required. Mode of a search.", "type": "string", "enum": ["SEARCH_MODE_UNSPECIFIED", "JOB_SEARCH", "FEATURED_JOB_SEARCH", "EMAIL_ALERT_SEARCH"], "enumDescriptions": ["The mode of the search method isn't specified.", "The job search doesn't include support for featured jobs.", "The job search matches only against featured jobs (jobs with a promotionValue > 0). This method doesn't return any jobs having a promotionValue <= 0. The search results order is determined by the promotionValue (jobs with a higher promotionValue are returned higher up in the search results), with relevance being used as a tiebreaker.", "Deprecated. Please use the SearchJobsForAlert API. The job search matches against jobs suited to email notifications."]}, "enableBroadening": {"type": "boolean", "description": "Optional. Controls whether to broaden the search when it produces sparse results. Broadened queries append results to the end of the matching results list. Defaults to false."}, "requestMetadata": {"$ref": "RequestMetadata", "description": "Required. The meta information collected about the job searcher, used to improve the search quality of the service. The identifiers, (such as `user_id`) are provided by users, and must be unique and consistent."}, "orderBy": {"enum": ["SORT_BY_UNSPECIFIED", "RELEVANCE_DESC", "PUBLISHED_DATE_DESC", "UPDATED_DATE_DESC", "TITLE", "TITLE_DESC", "ANNUALIZED_BASE_COMPENSATION", "ANNUALIZED_TOTAL_COMPENSATION", "ANNUALIZED_BASE_COMPENSATION_DESC", "ANNUALIZED_TOTAL_COMPENSATION_DESC"], "description": "Deprecated. Use sort_by instead. Optional. The criteria determining how search results are sorted. Defaults to SortBy.RELEVANCE_DESC if no value is specified.", "enumDescriptions": ["Default value.", "By descending relevance, as determined by the API algorithms. Relevance thresholding of query results is only available for queries if RELEVANCE_DESC sort ordering is specified.", "Sort by published date descending.", "Sort by updated date descending.", "Sort by job title ascending.", "Sort by job title descending.", "Sort by job annualized base compensation in ascending order. If job's annualized base compensation is unspecified, they are put at the end of search result.", "Sort by job annualized total compensation in ascending order. If job's annualized total compensation is unspecified, they are put at the end of search result.", "Sort by job annualized base compensation in descending order. If job's annualized base compensation is unspecified, they are put at the end of search result.", "Sort by job annualized total compensation in descending order. If job's annualized total compensation is unspecified, they are put at the end of search result."], "type": "string"}, "jobView": {"description": "Optional. The number of job attributes returned for jobs in the search response. Defaults to JobView.SMALL if no value is specified.", "enum": ["JOB_VIEW_UNSPECIFIED", "SMALL", "MINIMAL", "FULL"], "enumDescriptions": ["Default value.", "A small view of the job, with the following attributes in the search results: Job.name, Job.requisition_id, Job.job_title, Job.company_name, Job.job_locations, Job.description, Job.visibility. Note: Job.description is deprecated. It is scheduled to be removed from MatchingJob.Job objects in the SearchJobsResponse results on 12/31/2018.", "A minimal view of the job, with the following attributes in the search results: Job.name, Job.requisition_id, Job.job_title, Job.company_name, Job.job_locations.", "All available attributes are included in the search results. Note: [Job.description, Job.responsibilities, Job.qualifications and Job.incentives are deprecated. These fields are scheduled to be removed from MatchingJob.Job objects in the SearchJobsResponse results on 12/31/2018. See the alternative MatchingJob.search_text_snippet and MatchingJob.job_summary fields."], "type": "string"}, "offset": {"format": "int32", "description": "Optional. An integer that specifies the current offset (that is, starting result location, amongst the jobs deemed by the API as relevant) in search results. This field is only considered if page_token is unset. For example, 0 means to return results starting from the first matching job, and 10 means to return from the 11th job. This can be used for pagination, (for example, pageSize = 10 and offset = 10 means to return from the second page).", "type": "integer"}, "disableRelevanceThresholding": {"type": "boolean", "description": "Optional. Controls whether to disable relevance thresholding. Relevance thresholding removes jobs that have low relevance in search results, for example, removing \"Assistant to the CEO\" positions from the search results of a search for \"CEO\". Disabling relevance thresholding improves the accuracy of subsequent search requests. Defaults to false."}, "sortBy": {"description": "Optional. The criteria determining how search results are sorted. Defaults to SortBy.RELEVANCE_DESC if no value is specified.", "enumDescriptions": ["Default value.", "By descending relevance, as determined by the API algorithms. Relevance thresholding of query results is only available for queries if RELEVANCE_DESC sort ordering is specified.", "Sort by published date descending.", "Sort by updated date descending.", "Sort by job title ascending.", "Sort by job title descending.", "Sort by job annualized base compensation in ascending order. If job's annualized base compensation is unspecified, they are put at the end of search result.", "Sort by job annualized total compensation in ascending order. If job's annualized total compensation is unspecified, they are put at the end of search result.", "Sort by job annualized base compensation in descending order. If job's annualized base compensation is unspecified, they are put at the end of search result.", "Sort by job annualized total compensation in descending order. If job's annualized total compensation is unspecified, they are put at the end of search result."], "type": "string", "enum": ["SORT_BY_UNSPECIFIED", "RELEVANCE_DESC", "PUBLISHED_DATE_DESC", "UPDATED_DATE_DESC", "TITLE", "TITLE_DESC", "ANNUALIZED_BASE_COMPENSATION", "ANNUALIZED_TOTAL_COMPENSATION", "ANNUALIZED_BASE_COMPENSATION_DESC", "ANNUALIZED_TOTAL_COMPENSATION_DESC"]}}, "id": "SearchJobsRequest", "description": "Input only. The Request body of the `SearchJobs` call.", "type": "object"}, "CompletionResult": {"id": "CompletionResult", "description": "Output only. Resource that represents completion results.", "type": "object", "properties": {"type": {"description": "The completion topic.", "type": "string", "enumDescriptions": ["Default value.", "Only suggest job titles.", "Only suggest company names.", "Suggest both job titles and company names."], "enum": ["COMPLETION_TYPE_UNSPECIFIED", "JOB_TITLE", "COMPANY_NAME", "COMBINED"]}, "suggestion": {"type": "string", "description": "The suggestion for the query."}, "imageUrl": {"description": "The URL for the company logo if `type=COMPANY_NAME`.", "type": "string"}}}, "GetHistogramRequest": {"id": "GetHistogramRequest", "properties": {"filters": {"$ref": "JobFilters", "description": "Deprecated. Use query instead. Optional. Restrictions on the scope of the histogram."}, "requestMetadata": {"$ref": "RequestMetadata", "description": "Meta information, such as `user_id`, collected from the job searcher or other entity conducting a job search, is used to improve the service's search quality. Users determine identifier values, which must be unique and consist."}, "allowBroadening": {"description": "Optional. Controls whether to broaden the search to avoid too few results for a given query in instances where a search has sparse results. Results from a broadened query is a superset of the results from the original query. Defaults to false.", "type": "boolean"}, "searchTypes": {"type": "array", "description": "Required. A list of facets that specify the histogram data to be calculated against and returned. Histogram response times can be slow, and counts can be approximations. This call may be temporarily or permanently removed prior to the production release of Cloud Talent Solution.", "items": {"enum": ["JOB_FIELD_UNSPECIFIED", "COMPANY_ID", "EMPLOYMENT_TYPE", "COMPANY_SIZE", "DATE_PUBLISHED", "CUSTOM_FIELD_1", "CUSTOM_FIELD_2", "CUSTOM_FIELD_3", "CUSTOM_FIELD_4", "CUSTOM_FIELD_5", "CUSTOM_FIELD_6", "CUSTOM_FIELD_7", "CUSTOM_FIELD_8", "CUSTOM_FIELD_9", "CUSTOM_FIELD_10", "CUSTOM_FIELD_11", "CUSTOM_FIELD_12", "CUSTOM_FIELD_13", "CUSTOM_FIELD_14", "CUSTOM_FIELD_15", "CUSTOM_FIELD_16", "CUSTOM_FIELD_17", "CUSTOM_FIELD_18", "CUSTOM_FIELD_19", "CUSTOM_FIELD_20", "EDUCATION_LEVEL", "EXPERIENCE_LEVEL", "ADMIN1", "COUNTRY", "CITY", "LOCALE", "LANGUAGE", "CATEGORY", "CITY_COORDINATE", "ADMIN1_COUNTRY", "COMPANY_TITLE", "COMPANY_DISPLAY_NAME", "BASE_COMPENSATION_UNIT"], "enumDescriptions": ["The default value if search type is not specified.", "Filter by the company id field.", "Filter by the employment type field, such as `FULL_TIME` or `PART_TIME`.", "Filter by the company size type field, such as `BIG`, `SMALL` or `BIGGER`.", "Filter by the date published field. Values are stringified with TimeRange, for example, TimeRange.PAST_MONTH.", "Filter by custom field 1.", "Filter by custom field 2.", "Filter by custom field 3.", "Filter by custom field 4.", "Filter by custom field 5.", "Filter by custom field 6.", "Filter by custom field 7.", "Filter by custom field 8.", "Filter by custom field 9.", "Filter by custom field 10.", "Filter by custom field 11.", "Filter by custom field 12.", "Filter by custom field 13.", "Filter by custom field 14.", "Filter by custom field 15.", "Filter by custom field 16.", "Filter by custom field 17.", "Filter by custom field 18.", "Filter by custom field 19.", "Filter by custom field 20.", "Filter by the required education level of the job.", "Filter by the required experience level of the job.", "Filter by Admin1, which is a global placeholder for referring to state, province, or the particular term a country uses to define the geographic structure below the country level. Examples include states codes such as \"CA\", \"IL\", \"NY\", and provinces, such as \"BC\".", "Filter by the country code of job, such as US, JP, FR.", "Filter by the \"city name\", \"Admin1 code\", for example, \"Mountain View, CA\" or \"New York, NY\".", "Filter by the locale field of a job, such as \"en-US\", \"fr-FR\". This is the BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47).", "Filter by the language code portion of the locale field, such as \"en\" or \"fr\".", "Filter by the Category.", "Filter by the city center GPS coordinate (latitude and longitude), for example, 37.4038522,-122.0987765. Since the coordinates of a city center can change, clients may need to refresh them periodically.", "A combination of state or province code with a country code. This field differs from `JOB_ADMIN1`, which can be used in multiple countries.", "Deprecated. Use COMPANY_DISPLAY_NAME instead. Company display name.", "Company display name.", "Base compensation unit."], "type": "string"}}, "query": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Query used to search against jobs, such as keyword, location filters, etc."}}, "description": "Deprecated. Use SearchJobsRequest.histogram_facets instead to make a single call with both search and histogram. Input only. A request for the `GetHistogram` method.", "type": "object"}, "Money": {"type": "object", "properties": {"nanos": {"type": "integer", "description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32"}, "units": {"format": "int64", "type": "string", "description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar."}, "currencyCode": {"description": "The 3-letter currency code defined in ISO 4217.", "type": "string"}}, "description": "Represents an amount of money with its currency type.", "id": "Money"}, "SpellingCorrection": {"id": "SpellingCorrection", "type": "object", "properties": {"corrected": {"description": "Indicates if the query was corrected by the spell checker.", "type": "boolean"}, "correctedText": {"type": "string", "description": "Correction output consisting of the corrected keyword string."}}, "description": "Output only. Spell check result."}, "GoogleCloudTalentV4JobResult": {"description": "Mutation result of a job from a batch operation.", "type": "object", "properties": {"status": {"description": "The status of the job processed. This field is populated if the processing of the job fails.", "$ref": "Status"}, "job": {"description": "Here Job only contains basic information including name, company, language_code and requisition_id, use getJob method to retrieve detailed information of the created/updated job.", "$ref": "GoogleCloudTalentV4Job"}}, "id": "GoogleCloudTalentV4JobResult"}, "ExtendedCompensationFilter": {"id": "ExtendedCompensationFilter", "type": "object", "description": "Deprecated. Always use CompensationFilter. Input only. Filter on job compensation type and amount.", "properties": {"type": {"description": "Required. Type of filter.", "enum": ["FILTER_TYPE_UNSPECIFIED", "UNIT_ONLY", "UNIT_AND_AMOUNT", "ANNUALIZED_BASE_AMOUNT", "ANNUALIZED_TOTAL_AMOUNT"], "enumDescriptions": ["Filter type unspecified. Position holder, INVALID, should never be used.", "Filter by `base compensation entry's` unit. A job is a match if and only if the job contains a base CompensationEntry and the base CompensationEntry's unit matches provided compensation_units. Populate one or more compensation_units. See ExtendedCompensationInfo.CompensationEntry for definition of base compensation entry.", "Filter by `base compensation entry's` unit and amount / range. A job is a match if and only if the job contains a base CompensationEntry, and the base entry's unit matches provided compensation_units and amount or range overlaps with provided compensation_range. See ExtendedCompensationInfo.CompensationEntry for definition of base compensation entry. Set exactly one compensation_units and populate compensation_range.", "Filter by annualized base compensation amount and `base compensation entry's` unit. Populate compensation_range and zero or more compensation_units.", "Filter by annualized total compensation amount and `base compensation entry's` unit . Populate compensation_range and zero or more compensation_units."], "type": "string"}, "currency": {"type": "string", "description": "Optional. Specify currency in 3-letter [ISO 4217](https://www.iso.org/iso-4217-currency-codes.html) format. If unspecified, jobs are returned regardless of currency."}, "compensationUnits": {"description": "Required. Specify desired `base compensation entry's` ExtendedCompensationInfo.CompensationUnit.", "items": {"type": "string", "enum": ["EXTENDED_COMPENSATION_UNIT_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME", "OTHER_COMPENSATION_UNIT"], "enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_UNIT.", "Hourly.", "Daily.", "Weekly", "Monthly.", "Yearly.", "One time.", "Other compensation units."]}, "type": "array"}, "compensationRange": {"$ref": "ExtendedCompensationInfoCompensationRange", "description": "Optional. Compensation range."}, "includeJobWithUnspecifiedCompensationRange": {"type": "boolean", "description": "Optional. Whether to include jobs whose compensation range is unspecified."}}}, "ExtendedCompensationInfoCompensationRange": {"description": "Deprecated. See CompensationInfo. Compensation range.", "id": "ExtendedCompensationInfoCompensationRange", "properties": {"max": {"description": "Required. Maximum value.", "$ref": "ExtendedCompensationInfoDecimal"}, "min": {"description": "Required. Minimum value.", "$ref": "ExtendedCompensationInfoDecimal"}}, "type": "object"}, "GoogleCloudTalentV4JobApplicationInfo": {"type": "object", "description": "Application related details of a job posting.", "id": "GoogleCloudTalentV4JobApplicationInfo", "properties": {"emails": {"description": "Use this field to specify email address(es) to which resumes or applications can be sent. The maximum number of allowed characters for each entry is 255.", "type": "array", "items": {"type": "string"}}, "instruction": {"description": "Use this field to provide instructions, such as \"Mail your application to ...\", that a candidate can follow to apply for the job. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 3,000.", "type": "string"}, "uris": {"type": "array", "items": {"type": "string"}, "description": "Use this URI field to direct an applicant to a website, for example to link to an online application form. The maximum number of allowed characters for each entry is 2,000."}}}, "CompensationFilter": {"properties": {"type": {"enum": ["FILTER_TYPE_UNSPECIFIED", "UNIT_ONLY", "UNIT_AND_AMOUNT", "ANNUALIZED_BASE_AMOUNT", "ANNUALIZED_TOTAL_AMOUNT"], "description": "Required. Type of filter.", "type": "string", "enumDescriptions": ["Filter type unspecified. Position holder, INVALID, should never be used.", "Filter by `base compensation entry's` unit. A job is a match if and only if the job contains a base CompensationEntry and the base CompensationEntry's unit matches provided units. Populate one or more units. See CompensationInfo.CompensationEntry for definition of base compensation entry.", "Filter by `base compensation entry's` unit and amount / range. A job is a match if and only if the job contains a base CompensationEntry, and the base entry's unit matches provided compensation_units and amount or range overlaps with provided compensation_range. See CompensationInfo.CompensationEntry for definition of base compensation entry. Set exactly one units and populate range.", "Filter by annualized base compensation amount and `base compensation entry's` unit. Populate range and zero or more units.", "Filter by annualized total compensation amount and `base compensation entry's` unit . Populate range and zero or more units."]}, "units": {"items": {"enum": ["COMPENSATION_UNIT_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "YEARLY", "ONE_TIME", "OTHER_COMPENSATION_UNIT"], "enumDescriptions": ["Default value. Equivalent to OTHER_COMPENSATION_UNIT.", "Hourly.", "Daily.", "Weekly", "Monthly.", "Yearly.", "One time.", "Other compensation units."], "type": "string"}, "type": "array", "description": "Required. Specify desired `base compensation entry's` CompensationInfo.CompensationUnit."}, "includeJobsWithUnspecifiedCompensationRange": {"description": "Optional. Whether to include jobs whose compensation range is unspecified.", "type": "boolean"}, "range": {"$ref": "CompensationRange", "description": "Optional. Compensation range."}}, "type": "object", "id": "CompensationFilter", "description": "Input only. Filter on job compensation type and amount."}, "NamespacedDebugInput": {"id": "NamespacedDebugInput", "properties": {"disableExpTags": {"description": "Set of experiment tags to be disabled. All experiments that are tagged with one or more of these tags are disabled. If an experiment is disabled, it is never selected nor forced. If an aggregate experiment is disabled, its partitions are disabled together. If an experiment with an enrollment is disabled, the enrollment is disabled together.", "items": {"type": "string"}, "type": "array"}, "conditionallyForcedExpNames": {"items": {"type": "string"}, "description": "Set of experiment names to be conditionally forced. These experiments will be forced only if their conditions and their parent domain's conditions are true.", "type": "array"}, "absolutelyForcedExpTags": {"items": {"type": "string"}, "description": "Set of experiment tags to be absolutely forced. The experiments with these tags will be forced without evaluating the conditions.", "type": "array"}, "disableOrganicSelection": {"type": "boolean", "description": "If true, disable organic experiment selection (at all diversion points). Organic selection means experiment selection process based on traffic allocation and diversion condition evaluation. This does not disable selection of forced experiments. This is useful in cases when it is not known whether experiment selection behavior is responsible for a error or breakage. Disabling organic selection may help to isolate the cause of a given problem."}, "disableManualEnrollmentSelection": {"type": "boolean", "description": "If true, disable manual enrollment selection (at all diversion points). Manual enrollment selection means experiment selection process based on the request's manual enrollment states (a.k.a. opt-in experiments). This does not disable selection of forced experiments."}, "disableExps": {"items": {"type": "integer", "format": "int32"}, "type": "array", "description": "Set of experiment ids to be disabled. If an experiment is disabled, it is never selected nor forced. If an aggregate experiment is disabled, its partitions are disabled together. If an experiment with an enrollment is disabled, the enrollment is disabled together. If an ID corresponds to a domain, the domain itself and all descendant experiments and domains are disabled together."}, "absolutelyForcedExps": {"description": "Set of experiment ids to be absolutely forced. These ids will be forced without evaluating the conditions.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "forcedFlags": {"description": "Flags to force in a particular experiment state. Map from flag name to flag value.", "additionalProperties": {"type": "string"}, "type": "object"}, "conditionallyForcedExpTags": {"description": "Set of experiment tags to be conditionally forced. The experiments with these tags will be forced only if their conditions and their parent domain's conditions are true.", "items": {"type": "string"}, "type": "array"}, "disableExpNames": {"items": {"type": "string"}, "type": "array", "description": "Set of experiment names to be disabled. If an experiment is disabled, it is never selected nor forced. If an aggregate experiment is disabled, its partitions are disabled together. If an experiment with an enrollment is disabled, the enrollment is disabled together. If a name corresponds to a domain, the domain itself and all descendant experiments and domains are disabled together."}, "conditionallyForcedExps": {"description": "Set of experiment ids to be conditionally forced. These ids will be forced only if their conditions and their parent domain's conditions are true.", "type": "array", "items": {"type": "integer", "format": "int32"}}, "forcedRollouts": {"additionalProperties": {"type": "boolean"}, "description": "Rollouts to force in a particular experiment state. Map from rollout name to rollout value.", "type": "object"}, "absolutelyForcedExpNames": {"type": "array", "description": "Set of experiment names to be absolutely forced. These experiments will be forced without evaluating the conditions.", "items": {"type": "string"}}, "disableAutomaticEnrollmentSelection": {"description": "If true, disable automatic enrollment selection (at all diversion points). Automatic enrollment selection means experiment selection process based on the experiment's automatic enrollment condition. This does not disable selection of forced experiments.", "type": "boolean"}}, "description": "Next ID: 15", "type": "object"}, "CreateJobRequest": {"properties": {"processingOptions": {"$ref": "JobProcessingOptions", "description": "Optional. Options for job processing."}, "disableStreetAddressResolution": {"type": "boolean", "description": "Deprecated. Please use processing_options. This flag is ignored if processing_options is set. Optional. If set to `true`, the service does not attempt to resolve a more precise address for the job."}, "job": {"$ref": "Job", "description": "Required. The Job to be created."}}, "description": "Input only. Create job request.", "id": "CreateJobRequest", "type": "object"}, "RequestMetadata": {"description": "Input only. Meta information related to the job searcher or entity conducting the job search. This information is used to improve the performance of the service.", "type": "object", "id": "RequestMetadata", "properties": {"domain": {"type": "string", "description": "Required. The client-defined scope or source of the service call, which typically is the domain on which the service has been implemented and is currently being run. For example, if the service is being run by client *Foo, Inc.*, on job board www.foo.com and career site www.bar.com, then this field is set to \"foo.com\" for use on the job board, and \"bar.com\" for use on the career site. If this field is not available for some reason, send \"UNKNOWN\". Note that any improvements to the service model for a particular tenant site rely on this field being set correctly to some domain."}, "userId": {"type": "string", "description": "Required. A unique user identification string, as determined by the client. The client is responsible for ensuring client-level uniqueness of this value in order to have the strongest positive impact on search quality. Obfuscate this field for privacy concerns before providing it to the service. If this field is not available for some reason, please send \"UNKNOWN\". Note that any improvements to the service model for a particular tenant site, rely on this field being set correctly to some unique user_id."}, "sessionId": {"type": "string", "description": "Required. A unique session identification string. A session is defined as the duration of an end user's interaction with the service over a period. Obfuscate this field for privacy concerns before providing it to the API. If this field is not available for some reason, please send \"UNKNOWN\". Note that any improvements to the service model for a particular tenant site, rely on this field being set correctly to some unique session_id."}, "deviceInfo": {"description": "Optional. The type of device used by the job seeker at the time of the call to the service.", "$ref": "DeviceInfo"}}}, "GoogleCloudTalentV4BatchUpdateJobsResponse": {"type": "object", "properties": {"jobResults": {"description": "List of job mutation results from a batch update operation. It can change until operation status is FINISHED, FAILED or CANCELLED.", "type": "array", "items": {"$ref": "GoogleCloudTalentV4JobResult"}}}, "id": "GoogleCloudTalentV4BatchUpdateJobsResponse", "description": "The result of JobService.BatchUpdateJobs. It's used to replace google.longrunning.Operation.response in case of success."}, "JobLocation": {"id": "JobLocation", "properties": {"latLng": {"description": "An object representing a latitude/longitude pair.", "$ref": "LatLng"}, "postalAddress": {"description": "Postal address of the location that includes human readable information, such as postal delivery and payments addresses. Given a postal address, a postal service can deliver items to a premises, P.O. Box, or other delivery location.", "$ref": "PostalAddress"}, "locationType": {"enumDescriptions": ["Default value if the type is not specified.", "A country level location.", "A state or equivalent level location.", "A county or equivalent level location.", "A city or equivalent level location.", "A postal code level location.", "A sublocality is a subdivision of a locality, for example a city borough, ward, or arrondissement. Sublocalities are usually recognized by a local political authority. For example, Manhattan and Brooklyn are recognized as boroughs by the City of New York, and are therefore modeled as sublocalities.", "A district or equivalent level location.", "A smaller district or equivalent level display.", "A neighborhood level location.", "A street address level location."], "type": "string", "description": "The type of a location, which corresponds to the address lines field of PostalAddress. For example, \"Downtown, Atlanta, GA, USA\" has a type of LocationType#NEIGHBORHOOD, and \"Kansas City, KS, USA\" has a type of LocationType#LOCALITY.", "enum": ["LOCATION_TYPE_UNSPECIFIED", "COUNTRY", "ADMINISTRATIVE_AREA", "SUB_ADMINISTRATIVE_AREA", "LOCALITY", "POSTAL_CODE", "SUB_LOCALITY", "SUB_LOCALITY_1", "SUB_LOCALITY_2", "NEIGHBORHOOD", "STREET_ADDRESS"]}, "radiusMeters": {"format": "double", "type": "number", "description": "Radius in meters of the job location. This value is derived from the location bounding box in which a circle with the specified radius centered from LatLng coves the area associated with the job location. For example, currently, \"Mountain View, CA, USA\" has a radius of 7885.79 meters."}}, "description": "Output only. A resource that represents a location with full geographic information.", "type": "object"}, "ListJobsResponse": {"description": "Output only. List jobs response.", "type": "object", "properties": {"jobs": {"items": {"$ref": "Job"}, "description": "The Jobs for a given company. The maximum number of items returned is based on the limit field provided in the request.", "type": "array"}, "nextPageToken": {"type": "string", "description": "A token to retrieve the next page of results."}, "metadata": {"$ref": "ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}}, "id": "ListJobsResponse"}, "PostalAddress": {"properties": {"languageCode": {"description": "Optional. BCP-47 language code of the contents of this address (if known). This is often the UI language of the input form or is expected to match one of the languages used in the address' country/region, or their transliterated equivalents. This can affect formatting in certain countries, but is not critical to the correctness of the data and will never affect any validation or other non-formatting related operations. If this value is not known, it should be omitted (rather than specifying a possibly incorrect default). Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".", "type": "string"}, "sortingCode": {"description": "Optional. Additional, country-specific, sorting code. This is not used in most regions. Where it is used, the value is either a string like \"CEDEX\", optionally followed by a number (e.g. \"CEDEX 7\"), or just a number alone, representing the \"sector code\" (Jamaica), \"delivery area indicator\" (Malawi) or \"post office indicator\" (e.g. Côte d'Ivoire).", "type": "string"}, "administrativeArea": {"description": "Optional. Highest administrative subdivision which is used for postal addresses of a country or region. For example, this can be a state, a province, an oblast, or a prefecture. Specifically, for Spain this is the province and not the autonomous community (e.g. \"Barcelona\" and not \"Catalonia\"). Many countries don't use an administrative area in postal addresses. E.g. in Switzerland this should be left unpopulated.", "type": "string"}, "locality": {"description": "Optional. Generally refers to the city/town portion of the address. Examples: US city, IT comune, UK post town. In regions of the world where localities are not well defined or do not fit into this structure well, leave locality empty and use address_lines.", "type": "string"}, "sublocality": {"description": "Optional. Sublocality of the address. For example, this can be neighborhoods, boroughs, districts.", "type": "string"}, "recipients": {"description": "Optional. The recipient at the address. This field may, under certain circumstances, contain multiline information. For example, it might contain \"care of\" information.", "type": "array", "items": {"type": "string"}}, "organization": {"type": "string", "description": "Optional. The name of the organization at the address."}, "addressLines": {"description": "Unstructured address lines describing the lower levels of an address. Because values in address_lines do not have type information and may sometimes contain multiple values in a single field (e.g. \"Austin, TX\"), it is important that the line order is clear. The order of address lines should be \"envelope order\" for the country/region of the address. In places where this can vary (e.g. Japan), address_language is used to make it explicit (e.g. \"ja\" for large-to-small ordering and \"ja-Latn\" or \"en\" for small-to-large). This way, the most specific line of an address can be selected based on the language. The minimum permitted structural representation of an address consists of a region_code with all remaining information placed in the address_lines. It would be possible to format such an address very approximately without geocoding, but no semantic reasoning could be made about any of the address components until it was at least partially resolved. Creating an address only containing a region_code and address_lines, and then geocoding is the recommended way to handle completely unstructured addresses (as opposed to guessing which parts of the address should be localities or administrative areas).", "items": {"type": "string"}, "type": "array"}, "postalCode": {"description": "Optional. Postal code of the address. Not all countries use or require postal codes to be present, but where they are used, they may trigger additional validation with other parts of the address (e.g. state/zip validation in the U.S.A.).", "type": "string"}, "revision": {"description": "The schema revision of the `PostalAddress`. This must be set to 0, which is the latest revision. All new revisions **must** be backward compatible with old revisions.", "format": "int32", "type": "integer"}, "regionCode": {"type": "string", "description": "Required. CLDR region code of the country/region of the address. This is never inferred and it is up to the user to ensure the value is correct. See http://cldr.unicode.org/ and http://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: \"CH\" for Switzerland."}}, "description": "Represents a postal address, e.g. for postal delivery or payments addresses. Given a postal address, a postal service can deliver items to a premise, P.O. Box or similar. It is not intended to model geographical locations (roads, towns, mountains). In typical usage an address would be created via user input or from importing existing data, depending on the type of process. Advice on address input / editing: - Use an i18n-ready address widget such as https://github.com/google/libaddressinput) - Users should not be presented with UI elements for input or editing of fields outside countries where that field is used. For more guidance on how to use this schema, please see: https://support.google.com/business/answer/6397478", "id": "PostalAddress", "type": "object"}, "HistogramResults": {"description": "Output only. Histogram results that matches HistogramFacets specified in SearchJobsRequest.", "properties": {"compensationHistogramResults": {"items": {"$ref": "CompensationHistogramResult"}, "description": "Specifies compensation field-based histogram results that matches HistogramFacets.compensation_histogram_requests.", "type": "array"}, "simpleHistogramResults": {"description": "Specifies histogram results that matches HistogramFacets.simple_histogram_facets.", "items": {"$ref": "HistogramResult"}, "type": "array"}, "customAttributeHistogramResults": {"items": {"$ref": "CustomAttributeHistogramResult"}, "description": "Specifies histogram results for custom attributes that matches HistogramFacets.custom_attribute_histogram_facets.", "type": "array"}}, "type": "object", "id": "HistogramResults"}, "LocationFilter": {"properties": {"regionCode": {"type": "string", "description": "Optional. CLDR region code of the country/region of the address. This will be used to address ambiguity of the user-input location, e.g. \"Liverpool\" against \"Liverpool, NY, US\" or \"Liverpool, UK\". Set this field if all the jobs to search against are from a same region, or jobs are world-wide but the job seeker is from a specific region. See http://cldr.unicode.org/ and http://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: \"CH\" for Switzerland."}, "name": {"type": "string", "description": "Optional. The address name, such as \"Mountain View\" or \"Bay Area\"."}, "isTelecommute": {"type": "boolean", "description": "Optional. Allows the client to return jobs without a set location, specifically, telecommuting jobs (telecomuting is considered by the service as a special location. Job.allow_telecommute indicates if a job permits telecommuting. If this field is true, telecommuting jobs are searched, and name and lat_lng are ignored. This filter can be used by itself to search exclusively for telecommuting jobs, or it can be combined with another location filter to search for a combination of job locations, such as \"Mountain View\" or \"telecommuting\" jobs. However, when used in combination with other location filters, telecommuting jobs can be treated as less relevant than other jobs in the search response."}, "latLng": {"description": "Optional. The latitude and longitude of the geographic center from which to search. This field is ignored if `location_name` is provided.", "$ref": "LatLng"}, "distanceInMiles": {"type": "number", "description": "Optional. The distance_in_miles is applied when the location being searched for is identified as a city or smaller. When the location being searched for is a state or larger, this field is ignored.", "format": "double"}}, "type": "object", "id": "LocationFilter", "description": "Input only. Geographic region of the search."}, "GoogleCloudTalentV4BatchCreateJobsResponse": {"description": "The result of JobService.BatchCreateJobs. It's used to replace google.longrunning.Operation.response in case of success.", "type": "object", "id": "GoogleCloudTalentV4BatchCreateJobsResponse", "properties": {"jobResults": {"items": {"$ref": "GoogleCloudTalentV4JobResult"}, "description": "List of job mutation results from a batch create operation. It can change until operation status is FINISHED, FAILED or CANCELLED.", "type": "array"}}}, "ExtendedCompensationInfoDecimal": {"type": "object", "id": "ExtendedCompensationInfoDecimal", "description": "Deprecated. See CompensationInfo. Decimal number.", "properties": {"micros": {"format": "int32", "description": "Micro (10^-6) units. The value must be between -999,999 and +999,999 inclusive. If `units` is positive, `micros` must be positive or zero. If `units` is zero, `micros` can be positive, zero, or negative. If `units` is negative, `micros` must be negative or zero. For example -1.75 is represented as `units`=-1 and `micros`=-750,000.", "type": "integer"}, "units": {"description": "Whole units.", "format": "int64", "type": "string"}}}, "ExtendedCompensationInfo": {"id": "ExtendedCompensationInfo", "properties": {"annualizedTotalCompensationUnspecified": {"type": "boolean", "description": "Output only. Indicates annualized total compensation range cannot be derived, due to the job's all CompensationEntry cannot be annualized. See CompensationEntry for explanation on annualization and base compensation entry."}, "annualizedTotalCompensationRange": {"$ref": "ExtendedCompensationInfoCompensationRange", "description": "Output only. Annualized total compensation range."}, "annualizedBaseCompensationUnspecified": {"type": "boolean", "description": "Output only. Indicates annualized base compensation range cannot be derived, due to the job's base compensation entry cannot be annualized. See CompensationEntry for explanation on annualization and base compensation entry."}, "entries": {"description": "Optional. Job compensation information. At most one entry can be of type ExtendedCompensationInfo.CompensationType.BASE, which is referred as ** base compensation entry ** for the job.", "items": {"$ref": "ExtendedCompensationInfoCompensationEntry"}, "type": "array"}, "annualizedBaseCompensationRange": {"$ref": "ExtendedCompensationInfoCompensationRange", "description": "Output only. Annualized base compensation range."}, "currency": {"type": "string", "description": "Optional. A 3-letter [ISO 4217](https://www.iso.org/iso-4217-currency-codes.html) currency code."}}, "description": "Deprecated. Use CompensationInfo. Describes job compensation.", "type": "object"}, "CompanyInfoSource": {"id": "CompanyInfoSource", "description": "A resource that represents an external Google identifier for a company, for example, a Google+ business page or a Google Maps business page. For unsupported types, use `unknown_type_id`.", "properties": {"gplusId": {"type": "string", "description": "Optional. The numeric identifier for the employer's Google+ business page."}, "unknownTypeId": {"type": "string", "description": "Optional. A Google identifier that does not match any of the other types."}, "freebaseMid": {"type": "string", "description": "Optional. The Google's Knowledge Graph value for the employer's company."}, "mapsCid": {"type": "string", "description": "Optional. The numeric identifier for the employer's headquarters on Google Maps, namely, the Google Maps CID (cell id)."}}, "type": "object"}, "Job": {"description": "A Job resource represents a job posting (also referred to as a \"job listing\" or \"job requisition\"). A job belongs to a Company, which is the hiring entity responsible for the job.", "properties": {"languageCode": {"description": "Optional. The language of the posting. This field is distinct from any requirements for fluency that are associated with the job. Language codes must be in BCP-47 format, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47){: class=\"external\" target=\"_blank\" }. If this field is unspecified and Job.description is present, detected language code based on Job.description is assigned, otherwise defaults to 'en_US'.", "type": "string"}, "extendedCompensationInfo": {"description": "Deprecated. Always use compensation_info. Optional. Job compensation information. This field replaces compensation_info. Only CompensationInfo.entries or extended_compensation_info can be set, otherwise an exception is thrown.", "$ref": "ExtendedCompensationInfo"}, "createTime": {"description": "Output only. The timestamp when this job was created.", "format": "google-datetime", "type": "string"}, "requisitionId": {"type": "string", "description": "Required. The requisition ID, also referred to as the posting ID, assigned by the client to identify a job. This field is intended to be used by clients for client identification and tracking of listings. A job is not allowed to be created if there is another job with the same requisition_id, company_name and language_code. The maximum number of allowed characters is 255."}, "unindexedCustomFields": {"additionalProperties": {"$ref": "CustomField"}, "type": "object", "description": "Deprecated. Use custom_attributes instead. Optional. A map of fields to hold non-filterable custom job attributes, similar to filterable_custom_fields. These fields are distinct in that the data in these fields are not indexed. Therefore, the client cannot search against them, nor can the client use them to list jobs. The key of the map can be any valid string."}, "referenceUrl": {"type": "string", "description": "Output only. The URL of a web page that displays job details."}, "publishDate": {"description": "Optional. The date this job was most recently published in UTC format. The default value is the time the request arrives at the server.", "$ref": "Date"}, "educationLevels": {"type": "array", "description": "Optional. The desired education level for the job, such as \"Bachelors\", \"Masters\", \"Doctorate\".", "items": {"type": "string", "enum": ["EDUCATION_LEVEL_UNSPECIFIED", "HIGH_SCHOOL", "ASSOCIATE", "BACHELORS", "MASTERS", "DOCTORATE", "NO_DEGREE_REQUIRED"], "enumDescriptions": ["The default value if the level is not specified.", "A High School diploma is required for the position.", "An Associate degree is required for the position.", "A Bachelors degree is required for the position.", "A Masters degree is required for the position.", "A Doctorate degree is required for the position.", "No formal education is required for the position."]}}, "description": {"type": "string", "description": "Required. The description of the job, which typically includes a multi-paragraph description of the company and related information. Separate fields are provided on the job object for responsibilities, qualifications, and other job characteristics. Use of these separate job fields is recommended. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 100,000."}, "updateTime": {"type": "string", "description": "Output only. The timestamp when this job was last updated.", "format": "google-datetime"}, "endDate": {"description": "Optional. The end date of the job in UTC time zone. Typically this field is used for contracting engagements. Dates prior to 1970/1/1 and invalid date formats are ignored.", "$ref": "Date"}, "jobLocations": {"items": {"$ref": "JobLocation"}, "type": "array", "description": "Output only. Structured locations of the job, resolved from locations."}, "name": {"type": "string", "description": "Required during job update. Resource name assigned to a job by the API, for example, \"/jobs/foo\". Use of this field in job queries and API calls is preferred over the use of requisition_id since this value is unique."}, "visibility": {"enum": ["JOB_VISIBILITY_UNSPECIFIED", "PRIVATE", "GOOGLE", "PUBLIC"], "type": "string", "enumDescriptions": ["Default value.", "The Job is only visible to the owner.", "The Job is visible to the owner and may be visible to other applications and processes at Google. Not yet supported. Use PRIVATE.", "The Job is visible to the owner and may be visible to all other API clients. Not yet supported. Use PRIVATE."], "description": "Optional. The visibility of the job. Defaults to JobVisibility.PRIVATE if not specified. Currently only JobVisibility.PRIVATE is supported."}, "distributorCompanyId": {"description": "Optional but one of company_name or distributor_company_id must be provided. A unique company identifier used by job distributors to identify an employer's company entity. company_name takes precedence over this field, and is the recommended field to use to identify companies. The maximum number of allowed characters is 255.", "type": "string"}, "customAttributes": {"additionalProperties": {"$ref": "CustomAttribute"}, "description": "Optional. A map of fields to hold both filterable and non-filterable custom job attributes that are not covered by the provided structured fields. This field is a more general combination of the deprecated id-based filterable_custom_fields and string-based non_filterable_custom_fields. The keys of the map are strings up to 64 bytes and must match the pattern: a-zA-Z*. At most 100 filterable and at most 100 unfilterable keys are supported. For filterable `string_values`, across all keys at most 200 values are allowed, with each string no more than 255 characters. For unfilterable `string_values`, the maximum total size of `string_values` across all keys is 50KB.", "type": "object"}, "benefits": {"items": {"enumDescriptions": ["Default value if the type is not specified.", "The job includes access to programs that support child care, such as daycare.", "The job includes dental services that are covered by a dental insurance plan.", "The job offers specific benefits to domestic partners.", "The job allows for a flexible work schedule.", "The job includes health services that are covered by a medical insurance plan.", "The job includes a life insurance plan provided by the employer or available for purchase by the employee.", "The job allows for a leave of absence to a parent to care for a newborn child.", "The job includes a workplace retirement plan provided by the employer or available for purchase by the employee.", "The job allows for paid time off due to illness.", "Deprecated. Set Region.TELECOMMUTE instead. The job allows telecommuting (working remotely).", "The job includes paid time off for vacation.", "The job includes vision services that are covered by a vision insurance plan."], "enum": ["JOB_BENEFIT_TYPE_UNSPECIFIED", "CHILD_CARE", "DENTAL", "DOMESTIC_PARTNER", "FLEXIBLE_HOURS", "MEDICAL", "LIFE_INSURANCE", "PARENTAL_LEAVE", "RETIREMENT_PLAN", "SICK_DAYS", "TELECOMMUTE", "VACATION", "VISION"], "type": "string"}, "type": "array", "description": "Optional. The benefits included with the job."}, "jobTitle": {"type": "string", "description": "Required. The title of the job, such as \"Software Engineer\" The maximum number of allowed characters is 500."}, "companyDisplayName": {"description": "Output only. The name of the company listing the job.", "type": "string"}, "applicationInstruction": {"description": "Optional but at least one of application_urls, application_email_list or application_instruction must be specified. Use this field to provide instructions, such as \"Mail your application to ...\", that a candidate can follow to apply for the job. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 3,000.", "type": "string"}, "expiryDate": {"description": "Deprecated. Use expire_time instead. Optional but strongly recommended to be provided for the best service experience. The expiration date of the job in UTC time. After 12 am on this date, the job is marked as expired, and it no longer appears in search results. The expired job can't be deleted or listed by the DeleteJob and ListJobs APIs, but it can be retrieved with the GetJob API or updated with the UpdateJob API. An expired job can be updated and opened again by using a future expiration date. It can also remain expired. Updating an expired job to be open fails if there is another existing open job with same requisition_id, company_name and language_code. The expired jobs are retained in our system for 90 days. However, the overall expired job count cannot exceed 3 times the maximum of open jobs count over the past week, otherwise jobs with earlier expire time are removed first. Expired jobs are no longer accessible after they are cleaned out. A valid date range is between 1970/1/1 and 2100/12/31. Invalid dates are ignored and treated as expiry date not provided. If this value is not provided on job creation or is invalid, the job posting expires after 30 days from the job's creation time. For example, if the job was created on 2017/01/01 13:00AM UTC with an unspecified expiration date, the job expires after 2017/01/31 13:00AM UTC. If this value is not provided on job update, it depends on the field masks set by UpdateJobRequest.update_job_fields. If the field masks include expiry_date, or the masks are empty meaning that every field is updated, the job expires after 30 days from the job's last update time. Otherwise the expiration date isn't updated.", "$ref": "Date"}, "filterableCustomFields": {"type": "object", "additionalProperties": {"$ref": "CustomField"}, "description": "Deprecated. Use custom_attributes instead. Optional. A map of fields to hold filterable custom job attributes not captured by the standard fields such as job_title, company_name, or level. These custom fields store arbitrary string values, and can be used for purposes not covered by the structured fields. For the best search experience, use of the structured rather than custom fields is recommended. Data stored in these custom fields fields are indexed and searched against by keyword searches (see SearchJobsRequest.custom_field_filters][]). The map key must be a number between 1-20. If an invalid key is provided on job create or update, an error is returned."}, "employmentTypes": {"type": "array", "description": "Optional. The employment type(s) of a job, for example, full time or part time.", "items": {"enum": ["EMPLOYMENT_TYPE_UNSPECIFIED", "FULL_TIME", "PART_TIME", "CONTRACTOR", "TEMPORARY", "INTERN", "VOLUNTEER", "PER_DIEM", "CONTRACT_TO_HIRE", "FLY_IN_FLY_OUT", "OTHER"], "type": "string", "enumDescriptions": ["The default value if the employment type is not specified.", "The job requires working a number of hours that constitute full time employment, typically 40 or more hours per week.", "The job entails working fewer hours than a full time job, typically less than 40 hours a week.", "The job is offered as a contracted, as opposed to a salaried employee, position.", "The job is offered as a temporary employment opportunity, usually a short-term engagement.", "The job is a fixed-term opportunity for students or entry-level job seekers to obtain on-the-job training, typically offered as a summer position.", "The is an opportunity for an individual to volunteer, where there is no expectation of compensation for the provided services.", "The job requires an employee to work on an as-needed basis with a flexible schedule.", "The job is offered as a contracted position with the understanding that it is converted into a full-time position at the end of the contract. Jobs of this type are also returned by a search for EmploymentType.CONTRACTOR jobs.", "The job involves employing people in remote areas and flying them temporarily to the work site instead of relocating employees and their families permanently.", "The job does not fit any of the other listed types."]}}, "startDate": {"$ref": "Date", "description": "Optional. The start date of the job in UTC time zone. Typically this field is used for contracting engagements. Dates prior to 1970/1/1 and invalid date formats are ignored."}, "level": {"description": "Optional. The experience level associated with the job, such as \"Entry Level\".", "enum": ["JOB_LEVEL_UNSPECIFIED", "ENTRY_LEVEL", "EXPERIENCED", "MANAGER", "DIRECTOR", "EXECUTIVE"], "enumDescriptions": ["The default value if the level is not specified.", "Entry-level individual contributors, typically with less than 2 years of experience in a similar role. Includes interns.", "Experienced individual contributors, typically with 2+ years of experience in a similar role.", "Entry- to mid-level managers responsible for managing a team of people.", "Senior-level managers responsible for managing teams of managers.", "Executive-level managers and above, including C-level positions."], "type": "string"}, "promotionValue": {"type": "integer", "description": "Optional. A promotion value of the job, as determined by the client. The value determines the sort order of the jobs returned when searching for jobs using the featured jobs search call, with higher promotional values being returned first and ties being resolved by relevance sort. Only the jobs with a promotionValue >0 are returned in a FEATURED_JOB_SEARCH. Default value is 0, and negative values are treated as 0.", "format": "int32"}, "expireTime": {"format": "google-datetime", "type": "string", "description": "Optional but strongly recommended for the best service experience. The expiration timestamp of the job. After this timestamp, the job is marked as expired, and it no longer appears in search results. The expired job can't be deleted or listed by the DeleteJob and ListJobs APIs, but it can be retrieved with the GetJob API or updated with the UpdateJob API. An expired job can be updated and opened again by using a future expiration timestamp. Updating an expired job fails if there is another existing open job with same requisition_id, company_name and language_code. The expired jobs are retained in our system for 90 days. However, the overall expired job count cannot exceed 3 times the maximum of open jobs count over the past week, otherwise jobs with earlier expire time are cleaned first. Expired jobs are no longer accessible after they are cleaned out. The format of this field is RFC 3339 date strings. Example: 2000-01-01T00:00:00.999999999Z See [https://www.ietf.org/rfc/rfc3339.txt](https://www.ietf.org/rfc/rfc3339.txt). A valid date range is between 1970-01-01T00:00:00.0Z and 2100-12-31T23:59:59.999Z. Invalid dates are ignored and treated as expire time not provided. If this value is not provided at the time of job creation or is invalid, the job posting expires after 30 days from the job's creation time. For example, if the job was created on 2017/01/01 13:00AM UTC with an unspecified expiration date, the job expires after 2017/01/31 13:00AM UTC. If this value is not provided but expiry_date is, expiry_date is used. If this value is not provided on job update, it depends on the field masks set by UpdateJobRequest.update_job_fields. If the field masks include expiry_time, or the masks are empty meaning that every field is updated, the job posting expires after 30 days from the job's last update time. Otherwise the expiration date isn't updated."}, "department": {"description": "Optional. The department or functional area within the company with the open position. The maximum number of allowed characters is 255.", "type": "string"}, "companyTitle": {"description": "Deprecated. Use company_display_name instead. Output only. The name of the company listing the job.", "type": "string"}, "region": {"type": "string", "enumDescriptions": ["If the region is unspecified, the job is only returned if it matches the LocationFilter.", "In additiona to exact location matching, job is returned when the LocationFilter in search query is in the same state as this job. For example, if a `STATE_WIDE` job is posted in \"CA, USA\", it is returned if LocationFilter has \"Mountain View\".", "In addition to exact location matching, job is returned when LocationFilter in search query is in the same country as this job. For example, if a `NATION_WIDE` job is posted in \"USA\", it is returned if LocationFilter has 'Mountain View'.", "Job allows employees to work remotely (telecommute). If locations are provided with this value, the job is considered as having a location, but telecommuting is allowed."], "enum": ["REGION_UNSPECIFIED", "STATE_WIDE", "NATION_WIDE", "TELECOMMUTE"], "description": "Optional. The job Region (for example, state, country) throughout which the job is available. If this field is set, a LocationFilter in a search query within the job region finds this job if an exact location match is not specified. If this field is set, setting job locations to the same location level as this field is strongly recommended."}, "responsibilities": {"description": "Optional. A description of job responsibilities. The use of this field is recommended as an alternative to using the more general description field. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 10,000.", "type": "string"}, "applicationEmailList": {"description": "Optional but at least one of application_urls, application_email_list or application_instruction must be specified. Use this field to specify email address(es) to which resumes or applications can be sent. The maximum number of allowed characters is 255.", "items": {"type": "string"}, "type": "array"}, "applicationUrls": {"items": {"type": "string"}, "description": "Optional but at least one of application_urls, application_email_list or application_instruction must be specified. Use this URL field to direct an applicant to a website, for example to link to an online application form. The maximum number of allowed characters is 2,000.", "type": "array"}, "companyName": {"type": "string", "description": "Optional but one of company_name or distributor_company_id must be provided. The resource name of the company listing the job, such as /companies/foo. This field takes precedence over the distributor-assigned company identifier, distributor_company_id."}, "incentives": {"description": "Optional. A description of bonus, commission, and other compensation incentives associated with the job not including salary or pay. The maximum number of allowed characters is 10,000.", "type": "string"}, "locations": {"type": "array", "description": "Optional but strongly recommended for the best service experience. Location(s) where the emploeyer is looking to hire for this job posting. Specifying the full street address(es) of the hiring location enables better API results, especially job searches by commute time. At most 50 locations are allowed for best search performance. If a job has more locations, it is suggested to split it into multiple jobs with unique requisition_ids (e.g. 'ReqA' becomes 'ReqA-1', 'ReqA-2', etc.) as multiple jobs with the same requisition_id, company_name and language_code are not allowed. If the original requisition_id must be preserved, a custom field should be used for storage. It is also suggested to group the locations that close to each other in the same job for better search experience. The maximum number of allowed characters is 500.", "items": {"type": "string"}}, "qualifications": {"type": "string", "description": "Optional. A description of the qualifications required to perform the job. The use of this field is recommended as an alternative to using the more general description field. This field accepts and sanitizes HTML input, and also accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 10,000."}, "compensationInfo": {"description": "Optional. Job compensation information.", "$ref": "CompensationInfo"}}, "id": "Job", "type": "object"}, "CustomField": {"description": "Resource that represents the custom data not captured by the standard fields.", "id": "CustomField", "properties": {"values": {"description": "Optional. The values of the custom data.", "type": "array", "items": {"type": "string"}}}, "type": "object"}, "Company": {"id": "Company", "description": "A Company resource represents a company in the service. A company is the entity that owns job listings, that is, the hiring entity responsible for employing applicants for the job position.", "properties": {"keywordSearchableCustomAttributes": {"description": "Optional. A list of keys of filterable Job.custom_attributes, whose corresponding `string_values` are used in keyword search. Jobs with `string_values` under these specified field keys are returned if any of the values matches the search keyword. Custom field values with parenthesis, brackets and special symbols might not be properly searchable, and those keyword queries need to be surrounded by quotes.", "items": {"type": "string"}, "type": "array"}, "website": {"type": "string", "description": "Optional. The URL representing the company's primary web site or home page, such as, \"www.google.com\"."}, "eeoText": {"description": "Optional. Equal Employment Opportunity legal disclaimer text to be associated with all jobs, and typically to be displayed in all roles. The maximum number of allowed characters is 500.", "type": "string"}, "hiringAgency": {"description": "Optional. Set to true if it is the hiring agency that post jobs for other employers. Defaults to false if not provided.", "type": "boolean"}, "imageUrl": {"description": "Optional. A URL that hosts the employer's company logo. If provided, the logo image should be squared at 80x80 pixels. The url must be a Google Photos or Google Album url. Only images in these Google sub-domains are accepted.", "type": "string"}, "displayName": {"type": "string", "description": "Required. The name of the employer to be displayed with the job, for example, \"Google, LLC.\"."}, "title": {"description": "Deprecated. Use display_name instead. Required. The name of the employer to be displayed with the job, for example, \"Google, LLC.\".", "type": "string"}, "name": {"description": "Required during company update. The resource name for a company. This is generated by the service when a company is created, for example, \"companies/0000aaaa-1111-bbbb-2222-cccc3333dddd\".", "type": "string"}, "hqLocation": {"description": "Optional. The street address of the company's main headquarters, which may be different from the job location. The service attempts to geolocate the provided address, and populates a more specific location wherever possible in structured_company_hq_location.", "type": "string"}, "companySize": {"description": "Optional. The employer's company size.", "enum": ["COMPANY_SIZE_UNSPECIFIED", "MINI", "SMALL", "SMEDIUM", "MEDIUM", "BIG", "BIGGER", "GIANT"], "enumDescriptions": ["Default value if the size is not specified.", "The company has less than 50 employees.", "The company has between 50 and 99 employees.", "The company has between 100 and 499 employees.", "The company has between 500 and 999 employees.", "The company has between 1,000 and 4,999 employees.", "The company has between 5,000 and 9,999 employees.", "The company has 10,000 or more employees."], "type": "string"}, "keywordSearchableCustomFields": {"items": {"format": "int32", "type": "integer"}, "type": "array", "description": "Deprecated. Use keyword_searchable_custom_attributes instead. Optional. A list of filterable custom fields that should be used in keyword search. The jobs of this company are returned if any of these custom fields matches the search keyword. Custom field values with parenthesis, brackets and special symbols might not be properly searchable, and those keyword queries need to be surrounded by quotes."}, "companyInfoSources": {"items": {"$ref": "CompanyInfoSource"}, "type": "array", "description": "Optional. Identifiers external to the application that help to further identify the employer."}, "structuredCompanyHqLocation": {"$ref": "JobLocation", "description": "Output only. A structured headquarters location of the company, resolved from hq_location if possible."}, "distributorBillingCompanyId": {"type": "string", "description": "Optional. The unique company identifier provided by the client to identify an employer for billing purposes. Recommended practice is to use the distributor_company_id. Defaults to same value as distributor_company_id when a value is not provided."}, "suspended": {"description": "Output only. Indicates whether a company is flagged to be suspended from public availability by the service when job content appears suspicious, abusive, or spammy.", "type": "boolean"}, "careerPageLink": {"description": "Optional. The URL to employer's career site or careers page on the employer's web site.", "type": "string"}, "distributorCompanyId": {"type": "string", "description": "Required. A client's company identifier, used to uniquely identify the company. If an employer has a subsidiary or sub-brand, such as \"Alphabet\" and \"Google\", which the client wishes to use as the company displayed on the job. Best practice is to create a distinct company identifier for each distinct brand displayed. The maximum number of allowed characters is 255."}, "disableLocationOptimization": {"description": "Deprecated. Do not use this field. Optional. This field is no longer used. Any value set to it is ignored.", "type": "boolean"}}, "type": "object"}, "JobQuery": {"properties": {"customAttributeFilter": {"type": "string", "description": "Optional. This filter specifies a structured syntax to match against the Job.custom_attributes marked as `filterable`. The syntax for this expression is a subset of Google SQL syntax. Supported operators are: =, !=, <, <=, >, >= where the left of the operator is a custom field key and the right of the operator is a number or string (surrounded by quotes) value. Supported functions are LOWER() to perform case insensitive match and EMPTY() to filter on the existence of a key. Boolean expressions (AND/OR/NOT) are supported up to 3 levels of nesting (for example, \"((A AND B AND C) OR NOT D) AND E\"), a maximum of 50 comparisons/functions are allowed in the expression. The expression must be < 2000 characters in length. Sample Query: (key1 = \"TEST\" OR LOWER(key1)=\"test\" OR NOT EMPTY(key1)) AND key2 > 100"}, "locationFilters": {"description": "Optional. The location filter specifies geo-regions containing the jobs to search against. See LocationFilter for more information. If a location value isn't specified, jobs fitting the other search criteria are retrieved regardless of where they're located. If multiple values are specified, jobs are retrieved from any of the specified locations. If different values are specified for the LocationFilter.distance_in_miles parameter, the maximum provided distance is used for all locations. At most 5 location filters are allowed.", "items": {"$ref": "LocationFilter"}, "type": "array"}, "employmentTypes": {"items": {"enumDescriptions": ["The default value if the employment type is not specified.", "The job requires working a number of hours that constitute full time employment, typically 40 or more hours per week.", "The job entails working fewer hours than a full time job, typically less than 40 hours a week.", "The job is offered as a contracted, as opposed to a salaried employee, position.", "The job is offered as a temporary employment opportunity, usually a short-term engagement.", "The job is a fixed-term opportunity for students or entry-level job seekers to obtain on-the-job training, typically offered as a summer position.", "The is an opportunity for an individual to volunteer, where there is no expectation of compensation for the provided services.", "The job requires an employee to work on an as-needed basis with a flexible schedule.", "The job is offered as a contracted position with the understanding that it is converted into a full-time position at the end of the contract. Jobs of this type are also returned by a search for EmploymentType.CONTRACTOR jobs.", "The job involves employing people in remote areas and flying them temporarily to the work site instead of relocating employees and their families permanently.", "The job does not fit any of the other listed types."], "enum": ["EMPLOYMENT_TYPE_UNSPECIFIED", "FULL_TIME", "PART_TIME", "CONTRACTOR", "TEMPORARY", "INTERN", "VOLUNTEER", "PER_DIEM", "CONTRACT_TO_HIRE", "FLY_IN_FLY_OUT", "OTHER"], "type": "string"}, "type": "array", "description": "Optional. The employment type filter specifies the employment type of jobs to search against, such as EmploymentType.FULL_TIME. If a value is not specified, jobs in the search results include any employment type. If multiple values are specified, jobs in the search results include any of the specified employment types."}, "commuteFilter": {"$ref": "CommutePreference", "description": "Optional. Allows filtering jobs by commute time with different travel methods (for example, driving or public transit). Note: This only works with COMMUTE MODE. When specified, [JobQuery.location_filters] is ignored. Currently we don't support sorting by commute time."}, "compensationFilter": {"$ref": "CompensationFilter", "description": "Optional. This search filter is applied only to Job.compensation_info. For example, if the filter is specified as \"Hourly job with per-hour compensation > $15\", only jobs meeting these criteria are searched. If a filter isn't defined, all open jobs are searched."}, "query": {"type": "string", "description": "Optional. The query string that matches against the job title, description, and location fields. The maximum query size is 255 bytes."}, "companyDisplayNames": {"type": "array", "description": "Optional. This filter specifies the exact company display name of the jobs to search against. If a value isn't specified, jobs within the search results are associated with any company. If multiple values are specified, jobs within the search results may be associated with any of the specified companies. At most 20 company display name filters are allowed.", "items": {"type": "string"}}, "languageCodes": {"description": "Optional. This filter specifies the locale of jobs to search against, for example, \"en-US\". If a value isn't specified, the search results can contain jobs in any locale. Language codes should be in BCP-47 format, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47). At most 10 language code filters are allowed.", "items": {"type": "string"}, "type": "array"}, "publishDateRange": {"enum": ["DATE_RANGE_UNSPECIFIED", "PAST_24_HOURS", "PAST_WEEK", "PAST_MONTH", "PAST_YEAR", "PAST_3_DAYS"], "enumDescriptions": ["Default value: Filtering on time is not performed.", "The past 24 hours", "The past week (7 days)", "The past month (30 days)", "The past year (365 days)", "The past 3 days"], "description": "Optional. Jobs published within a range specified by this filter are searched against, for example, DateRange.PAST_MONTH. If a value isn't specified, all open jobs are searched against regardless of their published date.", "type": "string"}, "categories": {"description": "Optional. The category filter specifies the categories of jobs to search against. See Category for more information. If a value is not specified, jobs from any category are searched against. If multiple values are specified, jobs from any of the specified categories are searched against.", "items": {"type": "string", "enumDescriptions": ["The default value if the category is not specified.", "An accounting and finance job, such as an Accountant.", "And administrative and office job, such as an Administrative Assistant.", "An advertising and marketing job, such as Marketing Manager.", "An animal care job, such as Veterinarian.", "An art, fashion, or design job, such as Designer.", "A business operations job, such as Business Operations Manager.", "A cleaning and facilities job, such as Custodial Staff.", "A computer and IT job, such as Systems Administrator.", "A construction job, such as General Laborer.", "A customer service job, such s Cashier.", "An education job, such as School Teacher.", "An entertainment and travel job, such as Flight Attendant.", "A farming or outdoor job, such as Park Ranger.", "A healthcare job, such as Registered Nurse.", "A human resources job, such as Human Resources Director.", "An installation, maintenance, or repair job, such as Electrician.", "A legal job, such as Law Clerk.", "A management job, often used in conjunction with another category, such as Store Manager.", "A manufacturing or warehouse job, such as Assembly Technician.", "A media, communications, or writing job, such as Media Relations.", "An oil, gas or mining job, such as Offshore Driller.", "A personal care and services job, such as <PERSON> Stylist.", "A protective services job, such as Security Guard.", "A real estate job, such as Buyer's Agent.", "A restaurant and hospitality job, such as Restaurant Server.", "A sales and/or retail job, such Sales Associate.", "A science and engineering job, such as Lab Technician.", "A social services or non-profit job, such as Case Worker.", "A sports, fitness, or recreation job, such as Personal Trainer.", "A transportation or logistics job, such as Truck Driver."], "enum": ["JOB_CATEGORY_UNSPECIFIED", "ACCOUNTING_AND_FINANCE", "ADMINISTRATIVE_AND_OFFICE", "ADVERTISING_AND_MARKETING", "ANIMAL_CARE", "ART_FASHION_AND_DESIGN", "BUSINESS_OPERATIONS", "CLEANING_AND_FACILITIES", "COMPUTER_AND_IT", "CONSTRUCTION", "CUSTOMER_SERVICE", "EDUCATION", "ENTERTAINMENT_AND_TRAVEL", "FARMING_AND_OUTDOORS", "HEALTHCARE", "HUMAN_RESOURCES", "INSTALLATION_MAINTENANCE_AND_REPAIR", "LEGAL", "MANAGEMENT", "MANUFACTURING_AND_WAREHOUSE", "MEDIA_COMMUNICATIONS_AND_WRITING", "OIL_GAS_AND_MINING", "PERSONAL_CARE_AND_SERVICES", "PROTECTIVE_SERVICES", "REAL_ESTATE", "RESTAURANT_AND_HOSPITALITY", "SALES_AND_RETAIL", "SCIENCE_AND_ENGINEERING", "SOCIAL_SERVICES_AND_NON_PROFIT", "SPORTS_FITNESS_AND_RECREATION", "TRANSPORTATION_AND_LOGISTICS"]}, "type": "array"}, "companyNames": {"description": "Optional. This filter specifies the company entities to search against. If a value isn't specified, jobs are searched for against all companies. If multiple values are specified, jobs are searched against the companies specified. At most 20 company filters are allowed.", "items": {"type": "string"}, "type": "array"}, "disableSpellCheck": {"type": "boolean", "description": "Optional. This flag controls the spell-check feature. If false, the service attempts to correct a misspelled query, for example, \"enginee\" is corrected to \"engineer\". Defaults to false: a spell check is performed."}}, "description": "Input only. The query required to perform a search query or histogram.", "id": "<PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "CompensationRange": {"description": "Compensation range.", "type": "object", "id": "CompensationRange", "properties": {"min": {"$ref": "Money", "description": "Optional. The minimum amount of compensation. If left empty, the value is set to zero and the currency code is set to match the currency code of max_compensation."}, "max": {"description": "Optional. The maximum amount of compensation. If left empty, the value is set to a maximal compensation value and the currency code is set to match the currency code of min_compensation.", "$ref": "Money"}}}, "Date": {"id": "Date", "description": "Represents a whole or partial calendar date, e.g. a birthday. The time of day and time zone are either specified elsewhere or are not significant. The date is relative to the Proleptic Gregorian Calendar. This can represent: * A full date, with non-zero year, month and day values * A month and day value, with a zero year, e.g. an anniversary * A year on its own, with zero month and day values * A year and month value, with a zero day, e.g. a credit card expiration date Related types are google.type.TimeOfDay and `google.protobuf.Timestamp`.", "properties": {"day": {"format": "int32", "type": "integer", "description": "Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a year by itself or a year and month where the day is not significant."}, "month": {"type": "integer", "description": "Month of year. Must be from 1 to 12, or 0 if specifying a year without a month and day.", "format": "int32"}, "year": {"type": "integer", "description": "Year of date. Must be from 1 to 9999, or 0 if specifying a date without a year.", "format": "int32"}}, "type": "object"}, "UpdateJobRequest": {"description": "Input only. Update job request.", "id": "UpdateJobRequest", "properties": {"job": {"$ref": "Job", "description": "Required. The Job to be updated."}, "disableStreetAddressResolution": {"type": "boolean", "description": "Deprecated. Please use processing_options. This flag is ignored if processing_options is set. Optional. If set to `true`, the service does not attempt resolve a more precise address for the job."}, "updateJobFields": {"type": "string", "format": "google-fieldmask", "description": "Optional but strongly recommended to be provided for the best service experience. If update_job_fields is provided, only the specified fields in job are updated. Otherwise all the fields are updated. A field mask to restrict the fields that are updated. Valid values are: * jobTitle * employmentTypes * description * applicationUrls * applicationEmailList * applicationInstruction * responsibilities * qualifications * educationLevels * level * department * startDate * endDate * compensationInfo * incentives * languageCode * benefits * expireTime * customAttributes * visibility * publishDate * promotionValue * locations * region * expiryDate (deprecated) * filterableCustomFields (deprecated) * unindexedCustomFields (deprecated)"}, "processingOptions": {"$ref": "JobProcessingOptions", "description": "Optional. Options for job processing. UpdateJobRequest.disable_street_address_resolution is ignored if this flag is set."}}, "type": "object"}, "Filter": {"description": "Deprecated. Use BatchDeleteJobsRequest instead. Input only. Filter for jobs to be deleted.", "type": "object", "properties": {"requisitionId": {"description": "Required. The requisition ID (or posting ID) assigned by the client to identify a job. This is intended for client identification and tracking of listings. name takes precedence over this field The maximum number of allowed characters is 225.", "type": "string"}}, "id": "Filter"}, "CommutePreference": {"description": "Input only. Parameters needed for commute search.", "properties": {"roadTraffic": {"type": "string", "description": "Optional. Specifies the traffic density to use when calculating commute time. Must not be present if departure_hour_local is specified.", "enum": ["ROAD_TRAFFIC_UNSPECIFIED", "TRAFFIC_FREE", "BUSY_HOUR"], "enumDescriptions": ["Road traffic situation is not specified.", "Optimal commute time without considering any traffic impact.", "Commute time calculation takes in account the peak traffic impact."]}, "allowNonStreetLevelAddress": {"type": "boolean", "description": "Optional. If `true`, jobs without street level addresses may also be returned. For city level addresses, the city center is used. For state and coarser level addresses, text matching is used. If this field is set to `false` or is not specified, only jobs that include street level addresses will be returned by commute search."}, "departureHourLocal": {"type": "integer", "description": "Optional. The departure hour to use to calculate traffic impact. Accepts an integer between 0 and 23, representing the hour in the time zone of the start_location. Must not be present if road_traffic is specified.", "format": "int32"}, "travelTime": {"description": "Required. The maximum travel time in seconds. The maximum allowed value is `3600s` (one hour). Format is `123s`.", "format": "google-duration", "type": "string"}, "startLocation": {"$ref": "LatLng", "description": "Required. The latitude and longitude of the location from which to calculate the commute time."}, "method": {"enumDescriptions": ["Commute method is not specified.", "Commute time is calculated based on driving time.", "Commute time is calculated based on public transit including bus, metro, subway, etc."], "type": "string", "description": "Required. The method of transportation for which to calculate the commute time.", "enum": ["COMMUTE_METHOD_UNSPECIFIED", "DRIVING", "TRANSIT"]}}, "id": "CommutePreference", "type": "object"}, "BatchDeleteJobsRequest": {"type": "object", "id": "BatchDeleteJobsRequest", "description": "Input only. Batch delete jobs request.", "properties": {"filter": {"type": "string", "description": "Required. The filter string specifies the jobs to be deleted. Supported operator: =, AND The fields eligible for filtering are: * `companyName` (Required) * `requisitionId` (Required) Sample Query: companyName = \"companies/123\" AND requisitionId = \"req-1\""}}}, "GoogleCloudTalentV4Location": {"properties": {"radiusMiles": {"description": "Radius in miles of the job location. This value is derived from the location bounding box in which a circle with the specified radius centered from google.type.LatLng covers the area associated with the job location. For example, currently, \"Mountain View, CA, USA\" has a radius of 6.17 miles.", "format": "double", "type": "number"}, "latLng": {"description": "An object representing a latitude/longitude pair.", "$ref": "LatLng"}, "postalAddress": {"description": "Postal address of the location that includes human readable information, such as postal delivery and payments addresses. Given a postal address, a postal service can deliver items to a premises, P.O. Box, or other delivery location.", "$ref": "PostalAddress"}, "locationType": {"enum": ["LOCATION_TYPE_UNSPECIFIED", "COUNTRY", "ADMINISTRATIVE_AREA", "SUB_ADMINISTRATIVE_AREA", "LOCALITY", "POSTAL_CODE", "SUB_LOCALITY", "SUB_LOCALITY_1", "SUB_LOCALITY_2", "NEIGHBORHOOD", "STREET_ADDRESS"], "type": "string", "enumDescriptions": ["Default value if the type isn't specified.", "A country level location.", "A state or equivalent level location.", "A county or equivalent level location.", "A city or equivalent level location.", "A postal code level location.", "A sublocality is a subdivision of a locality, for example a city borough, ward, or arrondissement. Sublocalities are usually recognized by a local political authority. For example, Manhattan and Brooklyn are recognized as boroughs by the City of New York, and are therefore modeled as sublocalities.", "A district or equivalent level location.", "A smaller district or equivalent level display.", "A neighborhood level location.", "A street address level location."], "description": "The type of a location, which corresponds to the address lines field of google.type.PostalAddress. For example, \"Downtown, Atlanta, GA, USA\" has a type of LocationType.NEIGHBORHOOD, and \"Kansas City, KS, USA\" has a type of LocationType.LOCALITY."}}, "description": "A resource that represents a location with full geographic information.", "id": "GoogleCloudTalentV4Location", "type": "object"}, "MendelDebugInput": {"type": "object", "id": "MendelDebugInput", "description": "Message representing input to a Mendel server for debug forcing. See go/mendel-debug-forcing for more details. Next ID: 2", "properties": {"namespacedDebugInput": {"additionalProperties": {"$ref": "NamespacedDebugInput"}, "description": "When a request spans multiple servers, a MendelDebugInput may travel with the request and take effect in all the servers. This field is a map of namespaces to NamespacedMendelDebugInput protos. In a single server, up to two NamespacedMendelDebugInput protos are applied: 1. NamespacedMendelDebugInput with the global namespace (key == \"\"). 2. NamespacedMendelDebugInput with the server's namespace. When both NamespacedMendelDebugInput protos are present, they are merged. See go/mendel-debug-forcing for more details.", "type": "object"}}}}, "fullyEncodeReservedExpansion": true, "version": "v2", "discoveryVersion": "v1", "protocol": "rest", "ownerDomain": "google.com", "description": "Cloud Talent Solution provides the capability to create, read, update, and delete job postings, as well as search jobs based on keywords and filters. ", "servicePath": "", "title": "Cloud Talent Solution API", "parameters": {"oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "type": "string", "location": "query"}, "quotaUser": {"type": "string", "location": "query", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters."}, "$.xgafv": {"location": "query", "type": "string", "enumDescriptions": ["v1 error format", "v2 error format"], "description": "V1 error format.", "enum": ["1", "2"]}, "upload_protocol": {"location": "query", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string", "location": "query"}, "fields": {"location": "query", "description": "Selector specifying which fields to include in a partial response.", "type": "string"}, "access_token": {"location": "query", "description": "OAuth access token.", "type": "string"}, "callback": {"description": "JSONP", "type": "string", "location": "query"}, "alt": {"default": "json", "type": "string", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "description": "Data format for response.", "enum": ["json", "media", "proto"]}, "prettyPrint": {"type": "boolean", "location": "query", "description": "Returns response with indentations and line breaks.", "default": "true"}}, "baseUrl": "https://jobs.googleapis.com/", "kind": "discovery#restDescription", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/jobs": {"description": "Manage job postings"}}}}, "documentationLink": "https://cloud.google.com/talent-solution/job-search/docs/", "ownerName": "Google", "name": "jobs", "resources": {"jobs": {"methods": {"searchForAlert": {"description": "Searches for jobs using the provided SearchJobsRequest. This API call is intended for the use case of targeting passive job seekers (for example, job seekers who have signed up to receive email alerts about potential job opportunities), and has different algorithmic adjustments that are targeted to passive job seekers. This call constrains the visibility of jobs present in the database, and only returns jobs the caller has permission to search against.", "flatPath": "v2/jobs:searchForAlert", "path": "v2/jobs:searchForAlert", "response": {"$ref": "SearchJobsResponse"}, "request": {"$ref": "SearchJobsRequest"}, "parameters": {}, "id": "jobs.jobs.searchForAlert", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "httpMethod": "POST", "parameterOrder": []}, "get": {"parameterOrder": ["name"], "httpMethod": "GET", "path": "v2/{+name}", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "flatPath": "v2/jobs/{jobsId}", "description": "Retrieves the specified job, whose status is OPEN or recently EXPIRED within the last 90 days.", "parameters": {"name": {"required": true, "description": "Required. The resource name of the job to retrieve, such as \"jobs/11111111\".", "pattern": "^jobs/[^/]+$", "location": "path", "type": "string"}}, "response": {"$ref": "Job"}, "id": "jobs.jobs.get"}, "search": {"path": "v2/jobs:search", "httpMethod": "POST", "response": {"$ref": "SearchJobsResponse"}, "id": "jobs.jobs.search", "parameters": {}, "flatPath": "v2/jobs:search", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "description": "Searches for jobs using the provided SearchJobsRequest. This call constrains the visibility of jobs present in the database, and only returns jobs that the caller has permission to search against.", "parameterOrder": [], "request": {"$ref": "SearchJobsRequest"}}, "deleteByFilter": {"httpMethod": "POST", "parameterOrder": [], "response": {"$ref": "Empty"}, "path": "v2/jobs:deleteByFilter", "flatPath": "v2/jobs:deleteByFilter", "request": {"$ref": "DeleteJobsByFilterRequest"}, "parameters": {}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "id": "jobs.jobs.deleteByFilter", "description": "Deprecated. Use BatchDeleteJobs instead. Deletes the specified job by filter. You can specify whether to synchronously wait for validation, indexing, and general processing to be completed before the response is returned."}, "batchDelete": {"response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "request": {"$ref": "BatchDeleteJobsRequest"}, "httpMethod": "POST", "flatPath": "v2/jobs:batchDelete", "id": "jobs.jobs.batchDelete", "description": "Deletes a list of Job postings by filter.", "path": "v2/jobs:batchDelete", "parameterOrder": [], "parameters": {}}, "histogram": {"parameters": {}, "description": "Deprecated. Use SearchJobsRequest.histogram_facets instead to make a single call with both search and histogram. Retrieves a histogram for the given GetHistogramRequest. This call provides a structured count of jobs that match against the search query, grouped by specified facets. This call constrains the visibility of jobs present in the database, and only counts jobs the caller has permission to search against. For example, use this call to generate the number of jobs in the U.S. by state.", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "flatPath": "v2/jobs:histogram", "parameterOrder": [], "path": "v2/jobs:histogram", "request": {"$ref": "GetHistogramRequest"}, "httpMethod": "POST", "response": {"$ref": "GetHistogramResponse"}, "id": "jobs.jobs.histogram"}, "create": {"httpMethod": "POST", "response": {"$ref": "Job"}, "flatPath": "v2/jobs", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "description": "Creates a new job. Typically, the job becomes searchable within 10 seconds, but it may take up to 5 minutes.", "parameters": {}, "parameterOrder": [], "id": "jobs.jobs.create", "path": "v2/jobs", "request": {"$ref": "CreateJobRequest"}}, "patch": {"path": "v2/{+name}", "parameterOrder": ["name"], "request": {"$ref": "UpdateJobRequest"}, "httpMethod": "PATCH", "parameters": {"name": {"description": "Required during job update. Resource name assigned to a job by the API, for example, \"/jobs/foo\". Use of this field in job queries and API calls is preferred over the use of requisition_id since this value is unique.", "location": "path", "pattern": "^jobs/[^/]+$", "required": true, "type": "string"}}, "flatPath": "v2/jobs/{jobsId}", "id": "jobs.jobs.patch", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "response": {"$ref": "Job"}, "description": "Updates specified job. Typically, updated contents become visible in search results within 10 seconds, but it may take up to 5 minutes."}, "list": {"parameters": {"pageSize": {"format": "int32", "location": "query", "type": "integer", "description": "Optional. The maximum number of jobs to be returned per page of results. If ids_only is set to true, the maximum allowed page size is 1000. Otherwise, the maximum allowed page size is 100. Default is 100 if empty or a number < 1 is specified."}, "filter": {"location": "query", "type": "string", "description": "Required. The filter string specifies the jobs to be enumerated. Supported operator: =, AND The fields eligible for filtering are: * `companyName` (Required) * `requisitionId` (Optional) Sample Query: * companyName = \"companies/123\" * companyName = \"companies/123\" AND requisitionId = \"req-1\""}, "idsOnly": {"type": "boolean", "location": "query", "description": "Optional. If set to `true`, only Job.name, Job.requisition_id and Job.language_code will be returned. A typical use case is to synchronize job repositories. Defaults to false."}, "pageToken": {"description": "Optional. The starting point of a query result.", "type": "string", "location": "query"}}, "id": "jobs.jobs.list", "flatPath": "v2/jobs", "path": "v2/jobs", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "description": "Lists jobs by filter.", "httpMethod": "GET", "parameterOrder": [], "response": {"$ref": "ListJobsResponse"}}, "delete": {"path": "v2/{+name}", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "description": "Deletes the specified job. Typically, the job becomes unsearchable within 10 seconds, but it may take up to 5 minutes.", "parameters": {"disableFastProcess": {"description": "Deprecated. This field is not working anymore. Optional. If set to true, this call waits for all processing steps to complete before the job is cleaned up. Otherwise, the call returns while some steps are still taking place asynchronously, hence faster.", "type": "boolean", "location": "query"}, "name": {"location": "path", "type": "string", "pattern": "^jobs/[^/]+$", "required": true, "description": "Required. The resource name of the job to be deleted, such as \"jobs/11111111\"."}}, "response": {"$ref": "Empty"}, "id": "jobs.jobs.delete", "parameterOrder": ["name"], "flatPath": "v2/jobs/{jobsId}", "httpMethod": "DELETE"}}}, "companies": {"methods": {"delete": {"parameters": {"name": {"type": "string", "location": "path", "pattern": "^companies/[^/]+$", "description": "Required. The resource name of the company to be deleted, such as, \"companies/0000aaaa-1111-bbbb-2222-cccc3333dddd\".", "required": true}}, "path": "v2/{+name}", "response": {"$ref": "Empty"}, "id": "jobs.companies.delete", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "description": "Deletes the specified company.", "parameterOrder": ["name"], "httpMethod": "DELETE", "flatPath": "v2/companies/{companiesId}"}, "list": {"scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "parameterOrder": [], "description": "Lists all companies associated with a Cloud Talent Solution account.", "path": "v2/companies", "httpMethod": "GET", "response": {"$ref": "ListCompaniesResponse"}, "id": "jobs.companies.list", "parameters": {"pageToken": {"description": "Optional. The starting indicator from which to return results.", "location": "query", "type": "string"}, "pageSize": {"location": "query", "type": "integer", "format": "int32", "description": "Optional. The maximum number of companies to be returned, at most 100. Default is 100 if a non-positive number is provided."}, "mustHaveOpenJobs": {"description": "Optional. Set to true if the companies request must have open jobs. Defaults to false. If true, at most page_size of companies are fetched, among which only those with open jobs are returned.", "location": "query", "type": "boolean"}}, "flatPath": "v2/companies"}, "create": {"description": "Creates a new company entity.", "request": {"$ref": "Company"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "flatPath": "v2/companies", "path": "v2/companies", "id": "jobs.companies.create", "httpMethod": "POST", "response": {"$ref": "Company"}, "parameterOrder": [], "parameters": {}}, "patch": {"description": "Updates the specified company. Company names can't be updated. To update a company name, delete the company and all jobs associated with it, and only then re-create them.", "parameters": {"updateCompanyFields": {"type": "string", "format": "google-fieldmask", "description": "Optional but strongly recommended to be provided for the best service experience. If update_company_fields is provided, only the specified fields in company are updated. Otherwise all the fields are updated. A field mask to specify the company fields to update. Valid values are: * displayName * website * imageUrl * companySize * distributorBillingCompanyId * companyInfoSources * careerPageLink * hiringAgency * hqLocation * eeoText * keywordSearchableCustomAttributes * title (deprecated) * keywordSearchableCustomFields (deprecated)", "location": "query"}, "name": {"type": "string", "pattern": "^companies/[^/]+$", "required": true, "location": "path", "description": "Required during company update. The resource name for a company. This is generated by the service when a company is created, for example, \"companies/0000aaaa-1111-bbbb-2222-cccc3333dddd\"."}}, "id": "jobs.companies.patch", "path": "v2/{+name}", "flatPath": "v2/companies/{companiesId}", "httpMethod": "PATCH", "request": {"$ref": "Company"}, "parameterOrder": ["name"], "response": {"$ref": "Company"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"]}, "get": {"flatPath": "v2/companies/{companiesId}", "parameters": {"name": {"type": "string", "location": "path", "description": "Required. Resource name of the company to retrieve, such as \"companies/0000aaaa-1111-bbbb-2222-cccc3333dddd\".", "required": true, "pattern": "^companies/[^/]+$"}}, "description": "Retrieves the specified company.", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "parameterOrder": ["name"], "path": "v2/{+name}", "id": "jobs.companies.get", "response": {"$ref": "Company"}, "httpMethod": "GET"}}, "resources": {"jobs": {"methods": {"list": {"parameters": {"includeJobsCount": {"location": "query", "description": "Deprecated. Please DO NOT use this field except for small companies. Suggest counting jobs page by page instead. Optional. Set to true if the total number of open jobs is to be returned. Defaults to false.", "type": "boolean"}, "idsOnly": {"description": "Optional. If set to `true`, only job ID, job requisition ID and language code will be returned. A typical use is to synchronize job repositories. Defaults to false.", "location": "query", "type": "boolean"}, "jobRequisitionId": {"description": "Optional. The requisition ID, also known as posting ID, assigned by the company to the job. The maximum number of allowable characters is 225.", "type": "string", "location": "query"}, "pageToken": {"type": "string", "description": "Optional. The starting point of a query result.", "location": "query"}, "companyName": {"pattern": "^companies/[^/]+$", "required": true, "description": "Required. The resource name of the company that owns the jobs to be listed, such as, \"companies/0000aaaa-1111-bbbb-2222-cccc3333dddd\".", "location": "path", "type": "string"}, "pageSize": {"format": "int32", "description": "Optional. The maximum number of jobs to be returned per page of results. If ids_only is set to true, the maximum allowed page size is 1000. Otherwise, the maximum allowed page size is 100. Default is 100 if empty or a number < 1 is specified.", "type": "integer", "location": "query"}}, "httpMethod": "GET", "flatPath": "v2/companies/{companiesId}/jobs", "response": {"$ref": "ListCompanyJobsResponse"}, "id": "jobs.companies.jobs.list", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"], "path": "v2/{+companyName}/jobs", "parameterOrder": ["companyName"], "description": "Deprecated. Use ListJobs instead. Lists all jobs associated with a company."}}}}}, "v2": {"methods": {"complete": {"parameterOrder": [], "id": "jobs.complete", "description": "Completes the specified prefix with job keyword suggestions. Intended for use by a job search auto-complete search box.", "parameters": {"query": {"description": "Required. The query used to generate suggestions.", "location": "query", "type": "string"}, "type": {"enumDescriptions": ["Default value.", "Only suggest job titles.", "Only suggest company names.", "Suggest both job titles and company names."], "location": "query", "type": "string", "description": "Optional. The completion topic. The default is CompletionType.COMBINED.", "enum": ["COMPLETION_TYPE_UNSPECIFIED", "JOB_TITLE", "COMPANY_NAME", "COMBINED"]}, "languageCode": {"location": "query", "description": "Required. The language of the query. This is the BCP-47 language code, such as \"en-US\" or \"sr-Latn\". For more information, see [Tags for Identifying Languages](https://tools.ietf.org/html/bcp47). For CompletionType.JOB_TITLE type, only open jobs with same language_code are returned. For CompletionType.COMPANY_NAME type, only companies having open jobs with same language_code are returned. For CompletionType.COMBINED type, only open jobs with same language_code or companies having open jobs with same language_code are returned.", "type": "string"}, "scope": {"enum": ["COMPLETION_SCOPE_UNSPECIFIED", "TENANT", "PUBLIC"], "location": "query", "enumDescriptions": ["Default value.", "Suggestions are based only on the data provided by the client.", "Suggestions are based on all jobs data in the system that's visible to the client"], "type": "string", "description": "Optional. The scope of the completion. The defaults is CompletionScope.PUBLIC."}, "pageSize": {"description": "Required. Completion result count. The maximum allowed page size is 10.", "format": "int32", "type": "integer", "location": "query"}, "companyName": {"location": "query", "type": "string", "description": "Optional. If provided, restricts completion to the specified company."}}, "path": "v2:complete", "response": {"$ref": "CompleteQueryResponse"}, "httpMethod": "GET", "flatPath": "v2:complete", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/jobs"]}}}}}