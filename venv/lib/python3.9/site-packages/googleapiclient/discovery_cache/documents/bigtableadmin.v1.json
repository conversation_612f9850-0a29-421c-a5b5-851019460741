{"basePath": "", "baseUrl": "https://bigtableadmin.googleapis.com/", "batchPath": "batch", "canonicalName": "Bigtable Admin", "description": "Administer your Cloud Bigtable tables and instances.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/bigtable/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "bigtableadmin:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://bigtableadmin.mtls.googleapis.com/", "name": "big<PERSON><PERSON>min", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {}, "revision": "20220103", "rootUrl": "https://bigtableadmin.googleapis.com/", "schemas": {"AutoscalingLimits": {"description": "Limits for the number of nodes a Cluster can autoscale up/down to.", "id": "AutoscalingLimits", "properties": {"maxServeNodes": {"description": "Required. Maximum number of nodes to scale up to.", "format": "int32", "type": "integer"}, "minServeNodes": {"description": "Required. Minimum number of nodes to scale down to.", "format": "int32", "type": "integer"}}, "type": "object"}, "AutoscalingTargets": {"description": "The Autoscaling targets for a Cluster. These determine the recommended nodes.", "id": "AutoscalingTargets", "properties": {"cpuUtilizationPercent": {"description": "The cpu utilization that the Autoscaler should be trying to achieve. This number is on a scale from 0 (no utilization) to 100 (total utilization).", "format": "int32", "type": "integer"}}, "type": "object"}, "Backup": {"description": "A backup of a Cloud Bigtable table.", "id": "Backup", "properties": {"encryptionInfo": {"$ref": "EncryptionInfo", "description": "Output only. The encryption information for the backup.", "readOnly": true}, "endTime": {"description": "Output only. `end_time` is the time that the backup was finished. The row data in the backup will be no newer than this timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "expireTime": {"description": "Required. The expiration time of the backup, with microseconds granularity that must be at least 6 hours and at most 30 days from the time the request is received. Once the `expire_time` has passed, Cloud Bigtable will delete the backup and free the resources used by the backup.", "format": "google-datetime", "type": "string"}, "name": {"description": "A globally unique identifier for the backup which cannot be changed. Values are of the form `projects/{project}/instances/{instance}/clusters/{cluster}/ backups/_a-zA-Z0-9*` The final segment of the name must be between 1 and 50 characters in length. The backup is stored in the cluster identified by the prefix of the backup name of the form `projects/{project}/instances/{instance}/clusters/{cluster}`.", "type": "string"}, "sizeBytes": {"description": "Output only. Size of the backup in bytes.", "format": "int64", "readOnly": true, "type": "string"}, "sourceTable": {"description": "Required. Immutable. Name of the table from which this backup was created. This needs to be in the same instance as the backup. Values are of the form `projects/{project}/instances/{instance}/tables/{source_table}`.", "type": "string"}, "startTime": {"description": "Output only. `start_time` is the time that the backup was started (i.e. approximately the time the CreateBackup request is received). The row data in this backup will be no older than this timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the backup.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY"], "enumDescriptions": ["Not specified.", "The pending backup is still being created. Operations on the backup may fail with `FAILED_PRECONDITION` in this state.", "The backup is complete and ready for use."], "readOnly": true, "type": "string"}}, "type": "object"}, "BackupInfo": {"description": "Information about a backup.", "id": "BackupInfo", "properties": {"backup": {"description": "Output only. Name of the backup.", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. This time that the backup was finished. Row data in the backup will be no newer than this timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}, "sourceTable": {"description": "Output only. Name of the table the backup was created from.", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. The time that the backup was started. Row data in the backup will be no older than this timestamp.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Cluster": {"description": "A resizable group of nodes in a particular cloud location, capable of serving all Tables in the parent Instance.", "id": "Cluster", "properties": {"clusterConfig": {"$ref": "ClusterConfig", "description": "Configuration for this cluster."}, "defaultStorageType": {"description": "Immutable. The type of storage used by this cluster to serve its parent instance's tables, unless explicitly overridden.", "enum": ["STORAGE_TYPE_UNSPECIFIED", "SSD", "HDD"], "enumDescriptions": ["The user did not specify a storage type.", "Flash (SSD) storage should be used.", "Magnetic drive (HDD) storage should be used."], "type": "string"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Immutable. The encryption configuration for CMEK-protected clusters."}, "location": {"description": "Immutable. The location where this cluster's nodes and storage reside. For best performance, clients should be located as close as possible to this cluster. Currently only zones are supported, so values should be of the form `projects/{project}/locations/{zone}`.", "type": "string"}, "name": {"description": "The unique name of the cluster. Values are of the form `projects/{project}/instances/{instance}/clusters/a-z*`.", "type": "string"}, "serveNodes": {"description": "The number of nodes allocated to this cluster. More nodes enable higher throughput and more consistent performance.", "format": "int32", "type": "integer"}, "state": {"description": "Output only. The current state of the cluster.", "enum": ["STATE_NOT_KNOWN", "READY", "CREATING", "RESIZING", "DISABLED"], "enumDescriptions": ["The state of the cluster could not be determined.", "The cluster has been successfully created and is ready to serve requests.", "The cluster is currently being created, and may be destroyed if the creation process encounters an error. A cluster may not be able to serve requests while being created.", "The cluster is currently being resized, and may revert to its previous node count if the process encounters an error. A cluster is still capable of serving requests while being resized, but may exhibit performance as if its number of allocated nodes is between the starting and requested states.", "The cluster has no backing nodes. The data (tables) still exist, but no operations can be performed on the cluster."], "readOnly": true, "type": "string"}}, "type": "object"}, "ClusterAutoscalingConfig": {"description": "Autoscaling config for a cluster.", "id": "ClusterAutoscalingConfig", "properties": {"autoscalingLimits": {"$ref": "AutoscalingLimits", "description": "Required. Autoscaling limits for this cluster."}, "autoscalingTargets": {"$ref": "AutoscalingTargets", "description": "Required. Autoscaling targets for this cluster."}}, "type": "object"}, "ClusterConfig": {"description": "Configuration for a cluster.", "id": "ClusterConfig", "properties": {"clusterAutoscalingConfig": {"$ref": "ClusterAutoscalingConfig", "description": "Autoscaling configuration for this cluster."}}, "type": "object"}, "CreateBackupMetadata": {"description": "Metadata type for the operation returned by CreateBackup.", "id": "CreateBackupMetadata", "properties": {"endTime": {"description": "If set, the time at which this operation finished or was cancelled.", "format": "google-datetime", "type": "string"}, "name": {"description": "The name of the backup being created.", "type": "string"}, "sourceTable": {"description": "The name of the table the backup is created from.", "type": "string"}, "startTime": {"description": "The time at which this operation started.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CreateClusterMetadata": {"description": "The metadata for the Operation returned by CreateCluster.", "id": "CreateClusterMetadata", "properties": {"finishTime": {"description": "The time at which the operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "originalRequest": {"$ref": "CreateClusterRequest", "description": "The request that prompted the initiation of this CreateCluster operation."}, "requestTime": {"description": "The time at which the original request was received.", "format": "google-datetime", "type": "string"}, "tables": {"additionalProperties": {"$ref": "TableProgress"}, "description": "Keys: the full `name` of each table that existed in the instance when CreateCluster was first called, i.e. `projects//instances//tables/`. Any table added to the instance by a later API call will be created in the new cluster by that API call, not this one. Values: information on how much of a table's data has been copied to the newly-created cluster so far.", "type": "object"}}, "type": "object"}, "CreateClusterRequest": {"description": "Request message for BigtableInstanceAdmin.CreateCluster.", "id": "CreateClusterRequest", "properties": {"cluster": {"$ref": "Cluster", "description": "Required. The cluster to be created. Fields marked `OutputOnly` must be left blank."}, "clusterId": {"description": "Required. The ID to be used when referring to the new cluster within its instance, e.g., just `mycluster` rather than `projects/myproject/instances/myinstance/clusters/mycluster`.", "type": "string"}, "parent": {"description": "Required. The unique name of the instance in which to create the new cluster. Values are of the form `projects/{project}/instances/{instance}`.", "type": "string"}}, "type": "object"}, "CreateInstanceMetadata": {"description": "The metadata for the Operation returned by CreateInstance.", "id": "CreateInstanceMetadata", "properties": {"finishTime": {"description": "The time at which the operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "originalRequest": {"$ref": "CreateInstanceRequest", "description": "The request that prompted the initiation of this CreateInstance operation."}, "requestTime": {"description": "The time at which the original request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CreateInstanceRequest": {"description": "Request message for BigtableInstanceAdmin.CreateInstance.", "id": "CreateInstanceRequest", "properties": {"clusters": {"additionalProperties": {"$ref": "Cluster"}, "description": "Required. The clusters to be created within the instance, mapped by desired cluster ID, e.g., just `mycluster` rather than `projects/myproject/instances/myinstance/clusters/mycluster`. Fields marked `OutputOnly` must be left blank. Currently, at most four clusters can be specified.", "type": "object"}, "instance": {"$ref": "Instance", "description": "Required. The instance to create. Fields marked `OutputOnly` must be left blank."}, "instanceId": {"description": "Required. The ID to be used when referring to the new instance within its project, e.g., just `myinstance` rather than `projects/myproject/instances/myinstance`.", "type": "string"}, "parent": {"description": "Required. The unique name of the project in which to create the new instance. Values are of the form `projects/{project}`.", "type": "string"}}, "type": "object"}, "EncryptionConfig": {"description": "Cloud Key Management Service (Cloud KMS) settings for a CMEK-protected cluster.", "id": "EncryptionConfig", "properties": {"kmsKeyName": {"description": "Describes the Cloud KMS encryption key that will be used to protect the destination Bigtable cluster. The requirements for this key are: 1) The Cloud Bigtable service account associated with the project that contains this cluster must be granted the `cloudkms.cryptoKeyEncrypterDecrypter` role on the CMEK key. 2) Only regional keys can be used and the region of the CMEK key must match the region of the cluster. 3) All clusters within an instance must use the same CMEK key. Values are of the form `projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}`", "type": "string"}}, "type": "object"}, "EncryptionInfo": {"description": "Encryption information for a given resource. If this resource is protected with customer managed encryption, the in-use Cloud Key Management Service (Cloud KMS) key version is specified along with its status.", "id": "EncryptionInfo", "properties": {"encryptionStatus": {"$ref": "Status", "description": "Output only. The status of encrypt/decrypt calls on underlying data for this resource. Regardless of status, the existing data is always encrypted at rest.", "readOnly": true}, "encryptionType": {"description": "Output only. The type of encryption used to protect this resource.", "enum": ["ENCRYPTION_TYPE_UNSPECIFIED", "GOOGLE_DEFAULT_ENCRYPTION", "CUSTOMER_MANAGED_ENCRYPTION"], "enumDescriptions": ["Encryption type was not specified, though data at rest remains encrypted.", "The data backing this resource is encrypted at rest with a key that is fully managed by Google. No key version or status will be populated. This is the default state.", "The data backing this resource is encrypted at rest with a key that is managed by the customer. The in-use version of the key and its status are populated for CMEK-protected tables. CMEK-protected backups are pinned to the key version that was in use at the time the backup was taken. This key version is populated but its status is not tracked and is reported as `UNKNOWN`."], "readOnly": true, "type": "string"}, "kmsKeyVersion": {"description": "Output only. The version of the Cloud KMS key specified in the parent cluster that is in use for the data underlying this table.", "readOnly": true, "type": "string"}}, "type": "object"}, "Instance": {"description": "A collection of Bigtable Tables and the resources that serve them. All tables in an instance are served from all Clusters in the instance.", "id": "Instance", "properties": {"createTime": {"description": "Output only. A server-assigned timestamp representing when this Instance was created. For instances created before this field was added (August 2021), this value is `seconds: 0, nanos: 1`.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The descriptive name for this instance as it appears in UIs. Can be changed at any time, but should be kept globally unique to avoid confusion.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Required. Labels are a flexible and lightweight mechanism for organizing cloud resources into groups that reflect a customer's organizational needs and deployment strategies. They can be used to filter resources and aggregate metrics. * Label keys must be between 1 and 63 characters long and must conform to the regular expression: `\\p{Ll}\\p{Lo}{0,62}`. * Label values must be between 0 and 63 characters long and must conform to the regular expression: `[\\p{Ll}\\p{Lo}\\p{N}_-]{0,63}`. * No more than 64 labels can be associated with a given resource. * Keys and values must both be under 128 bytes.", "type": "object"}, "name": {"description": "The unique name of the instance. Values are of the form `projects/{project}/instances/a-z+[a-z0-9]`.", "type": "string"}, "state": {"description": "Output only. The current state of the instance.", "enum": ["STATE_NOT_KNOWN", "READY", "CREATING"], "enumDescriptions": ["The state of the instance could not be determined.", "The instance has been successfully created and can serve requests to its tables.", "The instance is currently being created, and may be destroyed if the creation process encounters an error."], "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of the instance. Defaults to `PRODUCTION`.", "enum": ["TYPE_UNSPECIFIED", "PRODUCTION", "DEVELOPMENT"], "enumDescriptions": ["The type of the instance is unspecified. If set when creating an instance, a `PRODUCTION` instance will be created. If set when updating an instance, the type will be left unchanged.", "An instance meant for production use. `serve_nodes` must be set on the cluster.", "DEPRECATED: Prefer PRODUCTION for all use cases, as it no longer enforces a higher minimum node count than DEVELOPMENT."], "type": "string"}}, "type": "object"}, "OperationProgress": {"description": "Encapsulates progress related information for a Cloud Bigtable long running operation.", "id": "OperationProgress", "properties": {"endTime": {"description": "If set, the time at which this operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "progressPercent": {"description": "Percent completion of the operation. Values are between 0 and 100 inclusive.", "format": "int32", "type": "integer"}, "startTime": {"description": "Time the request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "OptimizeRestoredTableMetadata": {"description": "Metadata type for the long-running operation used to track the progress of optimizations performed on a newly restored table. This long-running operation is automatically created by the system after the successful completion of a table restore, and cannot be cancelled.", "id": "OptimizeRestoredTableMetadata", "properties": {"name": {"description": "Name of the restored table being optimized.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the post-restore optimizations."}}, "type": "object"}, "PartialUpdateClusterMetadata": {"description": "The metadata for the Operation returned by PartialUpdateCluster.", "id": "PartialUpdateClusterMetadata", "properties": {"finishTime": {"description": "The time at which the operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "originalRequest": {"$ref": "PartialUpdateClusterRequest", "description": "The original request for PartialUpdateCluster."}, "requestTime": {"description": "The time at which the original request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "PartialUpdateClusterRequest": {"description": "Request message for BigtableInstanceAdmin.PartialUpdateCluster.", "id": "PartialUpdateClusterRequest", "properties": {"cluster": {"$ref": "Cluster", "description": "Required. The Cluster which contains the partial updates to be applied, subject to the update_mask."}, "updateMask": {"description": "Required. The subset of Cluster fields which should be replaced.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "PartialUpdateInstanceRequest": {"description": "Request message for BigtableInstanceAdmin.PartialUpdateInstance.", "id": "PartialUpdateInstanceRequest", "properties": {"instance": {"$ref": "Instance", "description": "Required. The Instance which will (partially) replace the current value."}, "updateMask": {"description": "Required. The subset of Instance fields which should be replaced. Must be explicitly set.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "RestoreTableMetadata": {"description": "Metadata type for the long-running operation returned by RestoreTable.", "id": "RestoreTableMetadata", "properties": {"backupInfo": {"$ref": "BackupInfo"}, "name": {"description": "Name of the table being created and restored to.", "type": "string"}, "optimizeTableOperationName": {"description": "If exists, the name of the long-running operation that will be used to track the post-restore optimization process to optimize the performance of the restored table. The metadata type of the long-running operation is OptimizeRestoreTableMetadata. The response type is Empty. This long-running operation may be automatically created by the system if applicable after the RestoreTable long-running operation completes successfully. This operation may not be created if the table is already optimized or the restore was not successful.", "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "The progress of the RestoreTable operation."}, "sourceType": {"description": "The type of the restore source.", "enum": ["RESTORE_SOURCE_TYPE_UNSPECIFIED", "BACKUP"], "enumDescriptions": ["No restore associated.", "A backup was used as the source of the restore."], "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TableProgress": {"description": "Progress info for copying a table's data to the new cluster.", "id": "TableProgress", "properties": {"estimatedCopiedBytes": {"description": "Estimate of the number of bytes copied so far for this table. This will eventually reach 'estimated_size_bytes' unless the table copy is CANCELLED.", "format": "int64", "type": "string"}, "estimatedSizeBytes": {"description": "Estimate of the size of the table to be copied.", "format": "int64", "type": "string"}, "state": {"enum": ["STATE_UNSPECIFIED", "PENDING", "COPYING", "COMPLETED", "CANCELLED"], "enumDescriptions": ["", "The table has not yet begun copying to the new cluster.", "The table is actively being copied to the new cluster.", "The table has been fully copied to the new cluster.", "The table was deleted before it finished copying to the new cluster. Note that tables deleted after completion will stay marked as COMPLETED, not CANCELLED."], "type": "string"}}, "type": "object"}, "UpdateAppProfileMetadata": {"description": "The metadata for the Operation returned by UpdateAppProfile.", "id": "UpdateAppProfileMetadata", "properties": {}, "type": "object"}, "UpdateClusterMetadata": {"description": "The metadata for the Operation returned by UpdateCluster.", "id": "UpdateClusterMetadata", "properties": {"finishTime": {"description": "The time at which the operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "originalRequest": {"$ref": "Cluster", "description": "The request that prompted the initiation of this UpdateCluster operation."}, "requestTime": {"description": "The time at which the original request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UpdateInstanceMetadata": {"description": "The metadata for the Operation returned by UpdateInstance.", "id": "UpdateInstanceMetadata", "properties": {"finishTime": {"description": "The time at which the operation failed or was completed successfully.", "format": "google-datetime", "type": "string"}, "originalRequest": {"$ref": "PartialUpdateInstanceRequest", "description": "The request that prompted the initiation of this UpdateInstance operation."}, "requestTime": {"description": "The time at which the original request was received.", "format": "google-datetime", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Bigtable Admin API", "version": "v1", "version_module": true}