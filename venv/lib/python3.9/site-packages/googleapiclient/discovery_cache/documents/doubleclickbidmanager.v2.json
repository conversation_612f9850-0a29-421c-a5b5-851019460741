{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/doubleclickbidmanager": {"description": "View and manage your reports in DoubleClick Bid Manager"}}}}, "basePath": "/v2/", "baseUrl": "https://doubleclickbidmanager.googleapis.com/v2/", "batchPath": "batch", "canonicalName": "DoubleClick Bid Manager", "description": "DoubleClick Bid Manager API allows users to manage and create campaigns and reports.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/bid-manager/", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "doubleclickbidmanager:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://doubleclickbidmanager.mtls.googleapis.com/", "name": "doubleclickbidmanager", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"queries": {"methods": {"create": {"description": "Creates a new query.", "flatPath": "queries", "httpMethod": "POST", "id": "doubleclickbidmanager.queries.create", "parameterOrder": [], "parameters": {}, "path": "queries", "request": {"$ref": "Query"}, "response": {"$ref": "Query"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}, "delete": {"description": "Deletes an existing query as well as its generated reports.", "flatPath": "queries/{queryId}", "httpMethod": "DELETE", "id": "doubleclickbidmanager.queries.delete", "parameterOrder": ["queryId"], "parameters": {"queryId": {"description": "Required. The ID of the query to delete.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "queries/{queryId}", "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}, "get": {"description": "Retrieves a query.", "flatPath": "queries/{queryId}", "httpMethod": "GET", "id": "doubleclickbidmanager.queries.get", "parameterOrder": ["queryId"], "parameters": {"queryId": {"description": "Required. The ID of the query to retrieve.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "queries/{queryId}", "response": {"$ref": "Query"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}, "list": {"description": "Lists queries created by the current user.", "flatPath": "queries", "httpMethod": "GET", "id": "doubleclickbidmanager.queries.list", "parameterOrder": [], "parameters": {"orderBy": {"description": "Field to sort the list by. Accepts the following values: * `queryId` (default) * `metadata.title` The default sorting order is ascending. To specify descending order for a field, add the suffix `desc` to the field name. For example, `queryId desc`.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results per page. Must be between `1` and `100`. Defaults to `100` if unspecified.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying which page of results the server should return. Typically, this is the value of nextPageToken, returned from the previous call to the `queries.list` method. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}}, "path": "queries", "response": {"$ref": "ListQueriesResponse"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}, "run": {"description": "Runs an existing query to generate a report.", "flatPath": "queries/{queryId}:run", "httpMethod": "POST", "id": "doubleclickbidmanager.queries.run", "parameterOrder": ["queryId"], "parameters": {"queryId": {"description": "Required. The ID of the query to run.", "format": "int64", "location": "path", "required": true, "type": "string"}, "synchronous": {"description": "Whether the query should be run synchronously. When `true`, the request won't return until the resulting report has finished running. This parameter is `false` by default. Setting this parameter to `true` is **not recommended**.", "location": "query", "type": "boolean"}}, "path": "queries/{queryId}:run", "request": {"$ref": "RunQueryRequest"}, "response": {"$ref": "Report"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}}, "resources": {"reports": {"methods": {"get": {"description": "Retrieves a report.", "flatPath": "queries/{queryId}/reports/{reportId}", "httpMethod": "GET", "id": "doubleclickbidmanager.queries.reports.get", "parameterOrder": ["queryId", "reportId"], "parameters": {"queryId": {"description": "Required. The ID of the query that generated the report.", "format": "int64", "location": "path", "required": true, "type": "string"}, "reportId": {"description": "Required. The ID of the query to retrieve.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "queries/{queryId}/reports/{reportId}", "response": {"$ref": "Report"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}, "list": {"description": "Lists reports generated by the provided query.", "flatPath": "queries/{queryId}/reports", "httpMethod": "GET", "id": "doubleclickbidmanager.queries.reports.list", "parameterOrder": ["queryId"], "parameters": {"orderBy": {"description": "Field to sort the list by. Accepts the following values: * `key.reportId` (default) The default sorting order is ascending. To specify descending order for a field, add the suffix `desc` to the field name. For example, `key.reportId desc`.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of results per page. Must be between `1` and `100`. Defaults to `100` if unspecified.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying which page of results the server should return. Typically, this is the value of nextPageToken returned from the previous call to the `queries.reports.list` method. If unspecified, the first page of results is returned.", "location": "query", "type": "string"}, "queryId": {"description": "Required. The ID of the query that generated the reports.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "queries/{queryId}/reports", "response": {"$ref": "ListReportsResponse"}, "scopes": ["https://www.googleapis.com/auth/doubleclickbidmanager"]}}}}}}, "revision": "20250305", "rootUrl": "https://doubleclickbidmanager.googleapis.com/", "schemas": {"DataRange": {"description": "The date range to be reported on.", "id": "DataRange", "properties": {"customEndDate": {"$ref": "Date", "description": "If `CUSTOM_DATES` is assigned to range, this field specifies the end date for the date range that is reported on. This field is required if using `CUSTOM_DATES` range and will be ignored otherwise."}, "customStartDate": {"$ref": "Date", "description": "If `CUSTOM_DATES` is assigned to range, this field specifies the starting date for the date range that is reported on. This field is required if using `CUSTOM_DATES` range and will be ignored otherwise."}, "range": {"description": "The preset date range to be reported on. If `CUSTOM_DATES` is assigned to this field, fields custom_start_date and custom_end_date must be set to specify the custom date range.", "enum": ["RANGE_UNSPECIFIED", "CUSTOM_DATES", "CURRENT_DAY", "PREVIOUS_DAY", "WEEK_TO_DATE", "MONTH_TO_DATE", "QUARTER_TO_DATE", "YEAR_TO_DATE", "PREVIOUS_WEEK", "PREVIOUS_MONTH", "PREVIOUS_QUARTER", "PREVIOUS_YEAR", "LAST_7_DAYS", "LAST_30_DAYS", "LAST_90_DAYS", "LAST_365_DAYS", "ALL_TIME", "LAST_14_DAYS", "LAST_60_DAYS"], "enumDescriptions": ["Default value when range is not specified or is unknown in this version.", "Custom date range.", "Current day.", "Previous day.", "All days, including the current day, since the most recent Sunday.", "All days, including the current day, since the start of the current month.", "All days, including the current day, since the start of the current quarter.", "All days, including the current day, since the start of the current calendar year.", "The previous completed week, beginning from Sunday.", "The previous completed calendar month.", "The previous completed quarter.", "The previous completed calendar year.", "The previous 7 days, excluding the current day.", "The previous 30 days, excluding the current day.", "The previous 90 days, excluding the current day.", "The previous 365 days, excluding the current day.", "All time for which data is available, excluding the current day.", "The previous 14 days, excluding the current day.", "The previous 60 days, excluding the current day."], "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "FilterPair": {"description": "Represents a single filter rule.", "id": "FilterPair", "properties": {"type": {"description": "The type of value to filter by. Defined by a [Filter](/bid-manager/reference/rest/v2/filters-metrics#filters) value.", "type": "string"}, "value": {"description": "The identifying value to filter by, such as a relevant resource ID.", "type": "string"}}, "type": "object"}, "ListQueriesResponse": {"id": "ListQueriesResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the page_token field in the subsequent call to `queries.list` method to retrieve the next page of results.", "type": "string"}, "queries": {"description": "The list of queries. This field will be absent if empty.", "items": {"$ref": "Query"}, "type": "array"}}, "type": "object"}, "ListReportsResponse": {"id": "ListReportsResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the page_token field in the subsequent call to `queries.reports.list` method to retrieve the next page of results.", "type": "string"}, "reports": {"description": "The list of reports. This field will be absent if empty.", "items": {"$ref": "Report"}, "type": "array"}}, "type": "object"}, "Options": {"description": "Report parameter options.", "id": "Options", "properties": {"includeOnlyTargetedUserLists": {"description": "Whether to include data for audience lists specifically targeted by filtered line items or insertion orders. Requires the use of `FILTER_INSERTION_ORDER` or `FILTER_LINE_ITEM` filters.", "type": "boolean"}}, "type": "object"}, "Parameters": {"description": "Parameters of a generated report.", "id": "Parameters", "properties": {"filters": {"description": "Filters to limit the scope of reported data.", "items": {"$ref": "FilterPair"}, "type": "array"}, "groupBys": {"description": "Dimensions by which to segment and group the data. Defined by [Filter](/bid-manager/reference/rest/v2/filters-metrics#filters) values.", "items": {"type": "string"}, "type": "array"}, "metrics": {"description": "Metrics to define the data populating the report. Defined by [Metric](/bid-manager/reference/rest/v2/filters-metrics#metrics) values.", "items": {"type": "string"}, "type": "array"}, "options": {"$ref": "Options", "description": "Additional report parameter options."}, "type": {"description": "The type of the report. The type of the report determines the dimesions, filters, and metrics that can be used.", "enum": ["REPORT_TYPE_UNSPECIFIED", "STANDARD", "INVENTORY_AVAILABILITY", "AUDIENCE_COMPOSITION", "FLOODLIGHT", "YOUTUBE", "GRP", "YOUTUBE_PROGRAMMATIC_GUARANTEED", "REACH", "UNIQUE_REACH_AUDIENCE", "FULL_PATH", "PATH_ATTRIBUTION"], "enumDeprecated": [false, false, true, true, false, false, false, false, false, false, true, true], "enumDescriptions": ["Default value when report type is not specified or is unknown in this version.", "Standard report.", "Inventory Availability report. See the [Display & Video 360 Help Center announcement](https://support.google.com/displayvideo/answer/15874746) for more information on this deprecation.", "Audience Composition report.", "Floodlight report.", "YouTube report.", "GRP report.", "YouTube Programmatic Guaranteed report.", "Reach report.", "Unique Reach Audience report.", "Full Path report.", "Path Attribution report."], "type": "string"}}, "type": "object"}, "Query": {"description": "A single query used to generate a report.", "id": "Query", "properties": {"metadata": {"$ref": "QueryMetadata", "description": "The metadata of the query."}, "params": {"$ref": "Parameters", "description": "The parameters of the report generated by the query."}, "queryId": {"description": "Output only. The unique ID of the query.", "format": "int64", "readOnly": true, "type": "string"}, "schedule": {"$ref": "QuerySchedule", "description": "When and how often the query is scheduled to run. If the frequency field is set to `ONE_TIME`, the query will only run when queries.run is called."}}, "type": "object"}, "QueryMetadata": {"description": "The metadata of the query.", "id": "QueryMetadata", "properties": {"dataRange": {"$ref": "DataRange", "description": "The date range the report generated by the query will report on. This date range will be defined by the time zone as used by the advertiser."}, "format": {"description": "The format of the report generated by the query.", "enum": ["FORMAT_UNSPECIFIED", "CSV", "XLSX"], "enumDescriptions": ["Default value when format is not specified or is unknown in this version.", "CSV.", "Excel."], "type": "string"}, "sendNotification": {"description": "Whether an email notification is sent to the query creator when a report generated by the query is ready. This value is `false` by default.", "type": "boolean"}, "shareEmailAddress": {"description": "List of additional email addresses with which to share the query. If send_notification is `true`, these email addresses will receive a notification when a report generated by the query is ready. If these email addresses are connected to Display & Video 360 users, the query will be available to them in the Display & Video 360 interface.", "items": {"type": "string"}, "type": "array"}, "title": {"description": "The display name of the query. This value will be used in the file name of reports generated by the query.", "type": "string"}}, "type": "object"}, "QuerySchedule": {"description": "Settings on when and how frequently to run a query.", "id": "QuerySchedule", "properties": {"endDate": {"$ref": "Date", "description": "The date on which to end the scheduled runs. This field is required if frequency is not set to `ONE_TIME`. Otherwise, it will be ignored."}, "frequency": {"description": "How frequently to run the query. If set to `ONE_TIME`, the query will only be run when queries.run is called.", "enum": ["FREQUENCY_UNSPECIFIED", "ONE_TIME", "DAILY", "WEEKLY", "SEMI_MONTHLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["Default value when frequency is not specified or is unknown in this version.", "Only when the query is run manually.", "Once a day.", "Once a week.", "Twice a month.", "Once a month.", "Once a quarter.", "Once a year."], "type": "string"}, "nextRunTimezoneCode": {"description": "The canonical code for the timezone the query schedule is based on. Scheduled runs are usually conducted in the morning of a given day. Defaults to `America/New_York`.", "type": "string"}, "startDate": {"$ref": "Date", "description": "The date on which to begin the scheduled runs. This field is required if frequency is not set to `ONE_TIME`. Otherwise, it will be ignored."}}, "type": "object"}, "Report": {"description": "A single report generated by its parent report.", "id": "Report", "properties": {"key": {"$ref": "ReportKey", "description": "The key information identifying the report."}, "metadata": {"$ref": "ReportMetadata", "description": "The metadata of the report."}, "params": {"$ref": "Parameters", "description": "The parameters of the report."}}, "type": "object"}, "ReportKey": {"description": "Identifying information of a report.", "id": "ReportKey", "properties": {"queryId": {"description": "Output only. The unique ID of the query that generated the report.", "format": "int64", "readOnly": true, "type": "string"}, "reportId": {"description": "Output only. The unique ID of the report.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "ReportMetadata": {"description": "The metadata of a report.", "id": "ReportMetadata", "properties": {"googleCloudStoragePath": {"description": "Output only. The location of the generated report file in Google Cloud Storage. This field will be absent if status.state is not `DONE`.", "readOnly": true, "type": "string"}, "reportDataEndDate": {"$ref": "Date", "description": "The end date of the report data date range."}, "reportDataStartDate": {"$ref": "Date", "description": "The start date of the report data date range."}, "status": {"$ref": "ReportStatus", "description": "The status of the report."}}, "type": "object"}, "ReportStatus": {"description": "The status of a report.", "id": "ReportStatus", "properties": {"finishTime": {"description": "Output only. The timestamp of when report generation finished successfully or in failure. This field will not be set unless state is `DONE` or `FAILED`.", "format": "google-datetime", "readOnly": true, "type": "string"}, "format": {"description": "The format of the generated report file.", "enum": ["FORMAT_UNSPECIFIED", "CSV", "XLSX"], "enumDescriptions": ["Default value when format is not specified or is unknown in this version.", "CSV.", "Excel."], "type": "string"}, "state": {"description": "Output only. The state of the report generation.", "enum": ["STATE_UNSPECIFIED", "QUEUED", "RUNNING", "DONE", "FAILED"], "enumDescriptions": ["Default value when state is not specified or is unknown in this version.", "The report is queued to run.", "The report is currently running.", "The report has finished running successfully.", "The report has finished running in failure."], "readOnly": true, "type": "string"}}, "type": "object"}, "RunQueryRequest": {"description": "Details specifying how to run a query.", "id": "RunQueryRequest", "properties": {"dataRange": {"$ref": "DataRange", "description": "The date range used by the query to generate the report. If unspecified, the query's original data_range is used."}}, "type": "object"}}, "servicePath": "v2/", "title": "DoubleClick Bid Manager API", "version": "v2"}