import os
from llm.ollama_client import ollama_generate
from llm.gemini_client import gemini_generate

class RAGPipeline:
    def __init__(self, retriever, llm_choice='ollama'):
        self.retriever = retriever
        self.llm_choice = llm_choice

    def set_llm(self, llm_choice):
        self.llm_choice = llm_choice

    def generate(self, query, context, file_map=None):
        # Check for line-based queries
        import re
        line_range = re.search(r'lines?\s*(\d+)\s*(?:to|-)\s*(\d+)', query, re.I)
        single_line = re.search(r'line\s*(\d+)', query, re.I)
        file_mention = re.search(r'file\s*[:=]?\s*([\w./\\-]+)', query, re.I)
        file_path = file_mention.group(1) if file_mention else None
        if line_range and file_map:
            start, end = int(line_range.group(1)), int(line_range.group(2))
            for lang, files in file_map.items():
                for f in files:
                    if file_path is None or file_path in f:
                        with open(f, 'r', encoding='utf-8', errors='ignore') as codef:
                            lines = codef.readlines()
                        snippet = ''.join(lines[start-1:end])
                        prompt = f"Explain the following code from {f}, lines {start}-{end}:\n{snippet}"
                        return self._llm_generate(prompt)
        elif single_line and file_map:
            line = int(single_line.group(1))
            for lang, files in file_map.items():
                for f in files:
                    if file_path is None or file_path in f:
                        with open(f, 'r', encoding='utf-8', errors='ignore') as codef:
                            lines = codef.readlines()
                        if 0 < line <= len(lines):
                            snippet = lines[line-1]
                            prompt = f"Explain this line from {f}, line {line}:\n{snippet}"
                            return self._llm_generate(prompt)
        # Default: context-aware answer
        prompt = f"Context:\n{context}\n\nQuery: {query}\nAnswer:"
        return self._llm_generate(prompt)

    def _llm_generate(self, prompt):
        if self.llm_choice == 'ollama':
            return ollama_generate(prompt)
        elif self.llm_choice == 'gemini':
            return gemini_generate(prompt)
        else:
            return 'LLM not configured.'
