"""Configuration management for RAG 2.0 Code Analyzer."""

import os
import logging
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class Config:
    """Configuration class for the RAG 2.0 Code Analyzer."""
    
    def __init__(self):
        """Initialize configuration from environment variables."""
        # LLM Provider Configuration
        self.llm_provider = os.getenv("LLM_PROVIDER", "gemini").lower()
        
        # Gemini Configuration
        self.gemini_api_key = os.getenv("GEMINI_API_KEY", "")
        
        # Ollama Configuration
        self.ollama_model = os.getenv("OLLAMA_MODEL", "starcoder2")
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        
        # RAG Configuration
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
        self.vector_db_type = os.getenv("VECTOR_DB_TYPE", "faiss").lower()
        self.max_chunk_size = int(os.getenv("MAX_CHUNK_SIZE", "1000"))
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        
        # File Analysis Configuration
        self.supported_extensions = {
            '.py': 'python',
            '.java': 'java', 
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.dart': 'dart'
        }
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate the configuration settings."""
        if self.llm_provider not in ['gemini', 'ollama']:
            logger.warning(f"Unknown LLM provider: {self.llm_provider}. Defaulting to gemini.")
            self.llm_provider = 'gemini'
        
        if self.llm_provider == 'gemini' and not self.gemini_api_key:
            logger.warning("Gemini API key not set. Gemini provider will not work.")
        
        if self.vector_db_type not in ['faiss', 'chroma', 'qdrant']:
            logger.warning(f"Unknown vector DB type: {self.vector_db_type}. Defaulting to faiss.")
            self.vector_db_type = 'faiss'
    
    def is_gemini_available(self):
        """Check if Gemini is properly configured."""
        return (self.gemini_api_key and 
                self.gemini_api_key != "your_gemini_api_key_here" and
                len(self.gemini_api_key.strip()) > 0)
    
    def is_ollama_available(self):
        """Check if Ollama is available."""
        try:
            import requests
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=2)
            return response.status_code == 200
        except:
            return False
    
    def get_provider_status(self):
        """Get the status of all LLM providers."""
        return {
            'gemini': '✅' if self.is_gemini_available() else '❌',
            'ollama': '✅' if self.is_ollama_available() else '❌'
        }
    
    def update_llm_provider(self, new_provider):
        """Update the LLM provider in .env file."""
        env_file = Path('.env')
        if not env_file.exists():
            logger.error(".env file not found!")
            return False
        
        try:
            # Read current content
            with open(env_file, 'r') as f:
                lines = f.readlines()
            
            # Update LLM_PROVIDER line
            updated = False
            for i, line in enumerate(lines):
                if line.startswith('LLM_PROVIDER='):
                    lines[i] = f'LLM_PROVIDER={new_provider}\n'
                    updated = True
                    break
            
            if not updated:
                # Add LLM_PROVIDER if not found
                lines.insert(0, f'LLM_PROVIDER={new_provider}\n')
            
            # Write back
            with open(env_file, 'w') as f:
                f.writelines(lines)
            
            # Update environment variable for immediate effect
            os.environ['LLM_PROVIDER'] = new_provider
            self.llm_provider = new_provider.lower()
            
            return True
        except Exception as e:
            logger.error(f"Error updating .env file: {e}")
            return False
    
    def get_language_from_extension(self, file_path):
        """Get programming language from file extension."""
        ext = Path(file_path).suffix.lower()
        return self.supported_extensions.get(ext, 'text')
    
    def is_supported_file(self, file_path):
        """Check if file is supported for analysis."""
        ext = Path(file_path).suffix.lower()
        return ext in self.supported_extensions
    
    def __str__(self):
        """String representation of configuration."""
        return f"""RAG 2.0 Configuration:
  LLM Provider: {self.llm_provider}
  Gemini Available: {self.is_gemini_available()}
  Ollama Available: {self.is_ollama_available()}
  Embedding Model: {self.embedding_model}
  Vector DB: {self.vector_db_type}
  Max Chunk Size: {self.max_chunk_size}
  Log Level: {self.log_level}"""


# Global configuration instance
config = Config()
