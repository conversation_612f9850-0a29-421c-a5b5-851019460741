"""Enhanced RAG Pipeline for Code Analysis."""

import os
import asyncio
import logging
import zipfile
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from collections import defaultdict

from config import config
from rag.pipeline import RAGPipeline
from rag.retriever import analyze_code_files, extract_files
from llm.gemini_client import gemini_generate, test_gemini_connection
from llm.ollama_client import ollama_generate

logger = logging.getLogger(__name__)


@dataclass
class AnalysisStatus:
    """Status of code analysis."""
    processed_files: int = 0
    total_chunks: int = 0
    total_entities: int = 0
    total_relationships: int = 0
    errors: List[str] = None
    languages_detected: Dict[str, int] = None
    file_analysis: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.languages_detected is None:
            self.languages_detected = {}
        if self.file_analysis is None:
            self.file_analysis = {}


@dataclass
class QueryResult:
    """Result of a codebase query."""
    llm_response: str = ""
    relevant_chunks: List[Any] = None
    confidence_score: float = 0.0
    processing_time: float = 0.0
    
    def __post_init__(self):
        if self.relevant_chunks is None:
            self.relevant_chunks = []


class EnhancedRAGPipeline:
    """Enhanced RAG Pipeline with improved accuracy and file analysis."""
    
    def __init__(self):
        """Initialize the enhanced RAG pipeline."""
        self.rag_pipeline = None
        self.documents = []
        self.file_map = {}
        self.language_stats = {}
        self.class_data = {}
        self.processed_files = []
        self.analysis_results = []
        self.initialized = False
    
    async def initialize(self):
        """Initialize the RAG pipeline."""
        try:
            # Test LLM connection
            if config.llm_provider == 'gemini':
                success, message = test_gemini_connection()
                if not success:
                    logger.error(f"Gemini connection failed: {message}")
                    raise Exception(f"Gemini API not available: {message}")
                logger.info("Gemini API connection successful")
            elif config.llm_provider == 'ollama':
                if not config.is_ollama_available():
                    raise Exception("Ollama is not available")
                logger.info("Ollama connection successful")
            
            # Initialize RAG pipeline with retriever
            from rag.retriever import analyze_code_files
            self.rag_pipeline = RAGPipeline(
                retriever=analyze_code_files,
                llm_choice=config.llm_provider
            )
            
            self.initialized = True
            logger.info(f"Enhanced RAG Pipeline initialized with {config.llm_provider}")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}")
            raise
    
    async def process_codebase(self, path: str) -> AnalysisStatus:
        """Process a codebase and return analysis status."""
        return await self._process_codebase_internal(path, clear_previous=False)
    
    async def process_codebase_fresh(self, path: str, clear_previous: bool = True) -> AnalysisStatus:
        """Process a codebase with option to clear previous data."""
        return await self._process_codebase_internal(path, clear_previous=clear_previous)
    
    async def _process_codebase_internal(self, path: str, clear_previous: bool = False) -> AnalysisStatus:
        """Internal method to process codebase."""
        if clear_previous:
            self._clear_data()
        
        status = AnalysisStatus()
        
        try:
            # Analyze code files
            results, lang_count, file_map = analyze_code_files(path)
            
            # Update internal state
            self.analysis_results.extend(results)
            self.language_stats.update(lang_count)
            self.file_map.update(file_map)
            
            # Process results
            for result in results:
                if 'error' not in result:
                    self.documents.append({
                        'file': result['path'],
                        'content': result['code'],
                        'language': result['lang'],
                        'entities': result['entities']
                    })
                    
                    # Extract class data
                    if result['entities']:
                        self.class_data.update(result['entities'])
                    
                    self.processed_files.append(result['file'])
                    status.processed_files += 1
                else:
                    status.errors.append(f"Error processing {result['file']}: {result['error']}")
            
            # Update status
            status.total_entities = len(self.class_data)
            status.languages_detected = dict(lang_count)
            status.file_analysis = self._generate_file_analysis()
            
            logger.info(f"Processed {status.processed_files} files, found {status.total_entities} entities")
            
        except Exception as e:
            logger.error(f"Error processing codebase: {e}")
            status.errors.append(str(e))
        
        return status
    
    def _clear_data(self):
        """Clear all processed data."""
        self.documents.clear()
        self.file_map.clear()
        self.language_stats.clear()
        self.class_data.clear()
        self.processed_files.clear()
        self.analysis_results.clear()
    
    def _generate_file_analysis(self) -> Dict[str, Any]:
        """Generate comprehensive file analysis."""
        analysis = {
            'total_files': len(self.processed_files),
            'languages': dict(self.language_stats),
            'file_types': {},
            'largest_files': [],
            'files_with_most_entities': []
        }
        
        # Analyze file types
        for doc in self.documents:
            ext = Path(doc['file']).suffix.lower()
            analysis['file_types'][ext] = analysis['file_types'].get(ext, 0) + 1
        
        # Find largest files and files with most entities
        file_sizes = []
        entity_counts = []
        
        for doc in self.documents:
            try:
                size = len(doc['content'])
                file_sizes.append((doc['file'], size))
                
                entity_count = len(doc.get('entities', {}))
                entity_counts.append((doc['file'], entity_count))
            except:
                continue
        
        # Sort and get top files
        analysis['largest_files'] = sorted(file_sizes, key=lambda x: x[1], reverse=True)[:5]
        analysis['files_with_most_entities'] = sorted(entity_counts, key=lambda x: x[1], reverse=True)[:5]
        
        return analysis
    
    async def query_codebase(self, query: str, analysis_type: str = "general", 
                           max_results: int = 10, file_filter: Optional[str] = None,
                           recent_files_only: bool = False) -> QueryResult:
        """Query the analyzed codebase with enhanced accuracy."""
        import time
        start_time = time.time()
        
        result = QueryResult()
        
        try:
            if not self.documents:
                result.llm_response = "No code has been analyzed yet. Please upload and analyze some code first."
                return result
            
            # Enhanced query processing
            response = await self._process_enhanced_query(query, analysis_type, file_filter)
            result.llm_response = response
            result.processing_time = time.time() - start_time
            result.confidence_score = self._calculate_confidence_score(query, response)
            
        except Exception as e:
            logger.error(f"Error querying codebase: {e}")
            result.llm_response = f"Error processing query: {str(e)}"
        
        return result
    
    async def _process_enhanced_query(self, query: str, analysis_type: str, file_filter: Optional[str]) -> str:
        """Process query with enhanced accuracy."""
        # Check for specific query patterns
        response = self._handle_special_queries(query, file_filter)
        if response:
            return response

        # Build context from analyzed code
        context = self._build_context(query, file_filter)

        # Create enhanced prompt based on analysis type
        prompt = self._create_enhanced_prompt(query, context, analysis_type)

        # Use the RAG pipeline for generation
        if self.rag_pipeline:
            response = self.rag_pipeline.generate(query, context, self.file_map)
        else:
            # Direct LLM call with enhanced prompt
            if config.llm_provider == 'gemini':
                response = gemini_generate(prompt)
            else:
                response = ollama_generate(prompt)

        return response

    def _handle_special_queries(self, query: str, file_filter: Optional[str]) -> Optional[str]:
        """Handle special query patterns like line-specific queries."""
        import re

        query_lower = query.lower().strip()

        # Handle line range queries (e.g., "explain lines 22 to 40")
        line_range_match = re.search(r'lines?\s*(\d+)\s*(?:to|through|-|:)\s*(\d+)', query_lower)
        if line_range_match:
            start_line = int(line_range_match.group(1))
            end_line = int(line_range_match.group(2))
            return self._get_lines_explanation(start_line, end_line, file_filter)

        # Handle single line queries (e.g., "explain line 88", "give line 88")
        single_line_match = re.search(r'(?:line|code.*line)\s*(\d+)', query_lower)
        if single_line_match:
            line_num = int(single_line_match.group(1))
            if 'give' in query_lower or 'show' in query_lower:
                return self._get_specific_line(line_num, file_filter)
            else:
                return self._get_lines_explanation(line_num, line_num, file_filter)

        # Handle class count queries
        class_count_queries = [
            "how many classes", "number of classes", "count classes", "class count",
            "how many classes are there", "total classes", "classes in codebase"
        ]
        if any(q in query_lower for q in class_count_queries):
            return self._get_class_count_info()

        # Handle function/method queries
        if 'functions' in query_lower or 'methods' in query_lower:
            return self._get_function_info(query_lower)

        return None

    def _get_lines_explanation(self, start_line: int, end_line: int, file_filter: Optional[str]) -> str:
        """Get explanation for specific line range."""
        target_files = self.documents
        if file_filter:
            target_files = [doc for doc in self.documents if file_filter.lower() in doc['file'].lower()]

        if not target_files:
            return f"No files found matching filter '{file_filter}'" if file_filter else "No files available for analysis."

        # Find the file with the requested lines
        for doc in target_files:
            lines = doc['content'].split('\n')
            if end_line <= len(lines):
                # Extract the requested lines
                if start_line == end_line:
                    code_snippet = lines[start_line - 1] if start_line <= len(lines) else ""
                    line_text = f"line {start_line}"
                else:
                    code_snippet = '\n'.join(lines[start_line - 1:end_line])
                    line_text = f"lines {start_line}-{end_line}"

                file_name = Path(doc['file']).name

                # Create detailed explanation prompt
                prompt = f"""Analyze and explain the following code from {file_name}, {line_text}:

```{doc['language']}
{code_snippet}
```

Please provide a detailed explanation that includes:
1. What this code does
2. How it works
3. Any important patterns or techniques used
4. Potential issues or improvements
5. Context within the larger codebase if relevant

Be specific and technical in your explanation."""

                if config.llm_provider == 'gemini':
                    return gemini_generate(prompt)
                else:
                    return ollama_generate(prompt)

        return f"Could not find {line_text} in the analyzed files. The files may have fewer lines than requested."

    def _get_specific_line(self, line_num: int, file_filter: Optional[str]) -> str:
        """Get specific line of code."""
        target_files = self.documents
        if file_filter:
            target_files = [doc for doc in self.documents if file_filter.lower() in doc['file'].lower()]

        if not target_files:
            return f"No files found matching filter '{file_filter}'" if file_filter else "No files available."

        for doc in target_files:
            lines = doc['content'].split('\n')
            if line_num <= len(lines):
                line_content = lines[line_num - 1]
                file_name = Path(doc['file']).name
                return f"**Line {line_num} from {file_name}:**\n```{doc['language']}\n{line_content}\n```"

        return f"Line {line_num} not found in the analyzed files."

    def _get_class_count_info(self) -> str:
        """Get information about classes in the codebase."""
        if not self.class_data:
            return "No classes found in the analyzed codebase."

        class_count = len(self.class_data)
        class_names = list(self.class_data.keys())

        response = f"**Found {class_count} classes in the analyzed codebase:**\n\n"

        # Group classes by file
        classes_by_file = {}
        for class_name, class_info in self.class_data.items():
            file_path = class_info.get('file', 'Unknown')
            file_name = Path(file_path).name if file_path != 'Unknown' else 'Unknown'
            if file_name not in classes_by_file:
                classes_by_file[file_name] = []
            classes_by_file[file_name].append(class_name)

        for file_name, classes in classes_by_file.items():
            response += f"**{file_name}:**\n"
            for class_name in classes:
                response += f"  • {class_name}\n"
            response += "\n"

        return response

    def _get_function_info(self, query: str) -> str:
        """Get information about functions/methods."""
        function_count = 0
        functions_by_file = {}

        for class_name, class_info in self.class_data.items():
            methods = class_info.get('methods', [])
            if methods:
                file_name = Path(class_info.get('file', 'Unknown')).name
                if file_name not in functions_by_file:
                    functions_by_file[file_name] = []
                functions_by_file[file_name].extend(methods)
                function_count += len(methods)

        if function_count == 0:
            return "No functions/methods found in the analyzed codebase."

        response = f"**Found {function_count} functions/methods in the analyzed codebase:**\n\n"

        for file_name, functions in functions_by_file.items():
            response += f"**{file_name}:**\n"
            for func in functions[:10]:  # Limit to first 10 per file
                response += f"  • {func}\n"
            if len(functions) > 10:
                response += f"  ... and {len(functions) - 10} more\n"
            response += "\n"

        return response

    def _create_enhanced_prompt(self, query: str, context: str, analysis_type: str) -> str:
        """Create an enhanced prompt based on analysis type."""
        base_prompt = f"Context:\n{context}\n\nQuery: {query}\n\n"

        if analysis_type == "explain":
            return base_prompt + "Please provide a detailed technical explanation of the code, including how it works, what it does, and any important patterns or techniques used."
        elif analysis_type == "debug":
            return base_prompt + "Analyze this code for potential bugs, issues, or improvements. Provide specific recommendations and solutions."
        elif analysis_type == "impact":
            return base_prompt + "Analyze the potential impact of changes to this code. Consider dependencies, side effects, and areas that might be affected."
        elif analysis_type == "similarity":
            return base_prompt + "Find and explain similar code patterns, functions, or structures in the codebase. Highlight commonalities and differences."
        else:  # general
            return base_prompt + "Please provide a comprehensive and accurate answer based on the code context provided."
    
    def _build_context(self, query: str, file_filter: Optional[str]) -> str:
        """Build context for the query from analyzed documents."""
        relevant_docs = self.documents
        
        # Apply file filter if provided
        if file_filter:
            relevant_docs = [doc for doc in self.documents 
                           if file_filter.lower() in doc['file'].lower()]
        
        # Build context string
        context_parts = []
        
        # Add language statistics
        if self.language_stats:
            lang_summary = ", ".join([f"{count} {lang} files" for lang, count in self.language_stats.items()])
            context_parts.append(f"Codebase contains: {lang_summary}")
        
        # Add class information
        if self.class_data:
            class_summary = f"Found {len(self.class_data)} classes: {', '.join(list(self.class_data.keys())[:10])}"
            if len(self.class_data) > 10:
                class_summary += "..."
            context_parts.append(class_summary)
        
        # Add relevant code snippets (limited to avoid token limits)
        for i, doc in enumerate(relevant_docs[:5]):  # Limit to 5 most relevant files
            file_name = Path(doc['file']).name
            content_preview = doc['content'][:1000] + "..." if len(doc['content']) > 1000 else doc['content']
            context_parts.append(f"\nFile: {file_name}\nLanguage: {doc['language']}\nContent:\n{content_preview}")
        
        return "\n\n".join(context_parts)
    
    def _calculate_confidence_score(self, query: str, response: str) -> float:
        """Calculate confidence score for the response."""
        # Simple heuristic-based confidence calculation
        confidence = 0.5  # Base confidence
        
        # Increase confidence if response contains code
        if "```" in response or "def " in response or "class " in response:
            confidence += 0.2
        
        # Increase confidence if response is detailed
        if len(response) > 100:
            confidence += 0.1
        
        # Decrease confidence if response contains error indicators
        if any(word in response.lower() for word in ["error", "sorry", "don't know", "unclear"]):
            confidence -= 0.3
        
        return max(0.0, min(1.0, confidence))
