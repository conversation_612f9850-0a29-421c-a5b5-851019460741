import requests
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
GEMINI_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

def gemini_generate(prompt):
    """
    Generate text using Google's Gemini API.

    Args:
        prompt (str): The input prompt for text generation

    Returns:
        str: Generated text response or error message
    """
    if not GEMINI_API_KEY or GEMINI_API_KEY == "your_gemini_api_key_here":
        return "Error: GEMINI_API_KEY not set in .env file or using placeholder value."

    # Gemini API uses API key as query parameter, not Bearer token
    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": prompt
                    }
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "topK": 40,
            "topP": 0.95,
            "maxOutputTokens": 4096,
            "stopSequences": []
        },
        "safetySettings": [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
    }

    try:
        # Make request with API key as query parameter
        url = f"{GEMINI_URL}?key={GEMINI_API_KEY}"
        resp = requests.post(url, json=data, headers=headers, timeout=30)

        # Log the response for debugging
        logger.debug(f"Gemini API response status: {resp.status_code}")

        if resp.status_code == 200:
            response_json = resp.json()
            candidates = response_json.get("candidates", [])

            if candidates and len(candidates) > 0:
                candidate = candidates[0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if parts and len(parts) > 0 and "text" in parts[0]:
                        return parts[0]["text"]
                    else:
                        return "Error: No text content in Gemini response."
                else:
                    # Check if content was blocked
                    if "finishReason" in candidate:
                        finish_reason = candidate["finishReason"]
                        if finish_reason == "SAFETY":
                            return "Error: Content was blocked by Gemini's safety filters."
                        elif finish_reason == "RECITATION":
                            return "Error: Content was blocked due to recitation concerns."
                        else:
                            return f"Error: Generation stopped with reason: {finish_reason}"
                    return "Error: Invalid response structure from Gemini API."
            else:
                return "Error: No candidates in Gemini response."
        else:
            error_text = resp.text
            logger.error(f"Gemini API error: {resp.status_code} - {error_text}")

            # Parse error response for better error messages
            try:
                error_json = resp.json()
                if "error" in error_json:
                    error_message = error_json["error"].get("message", "Unknown error")
                    return f"Gemini API error: {error_message}"
            except:
                pass

            return f"Gemini API error: HTTP {resp.status_code} - {error_text}"

    except requests.exceptions.Timeout:
        return "Error: Gemini API request timed out. Please try again."
    except requests.exceptions.ConnectionError:
        return "Error: Could not connect to Gemini API. Please check your internet connection."
    except requests.exceptions.RequestException as e:
        return f"Gemini API request error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error in gemini_generate: {str(e)}")
        return f"Unexpected error: {str(e)}"


def test_gemini_connection():
    """
    Test the Gemini API connection with a simple prompt.

    Returns:
        tuple: (success: bool, message: str)
    """
    if not GEMINI_API_KEY or GEMINI_API_KEY == "your_gemini_api_key_here":
        return False, "GEMINI_API_KEY not set or using placeholder value"

    test_prompt = "Hello, please respond with 'Gemini API is working correctly.'"
    response = gemini_generate(test_prompt)

    if response.startswith("Error:"):
        return False, response
    else:
        return True, "Gemini API connection successful"